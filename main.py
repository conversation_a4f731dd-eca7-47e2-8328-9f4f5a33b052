#!/usr/bin/env python3
"""
FinRL Stock Trading Agent - Main CLI Entry Point

A sophisticated stock trading agent that trades the top 10 tech stocks using
reinforcement learning algorithms with an Asymmetric Return Profile strategy.

Usage:
    python main.py get-data [--symbols SYMBOLS] [--start-date DATE] [--end-date DATE]
    python main.py process-data [--input-dir DIR] [--output-dir DIR]
    python main.py tune [--trials N] [--study-name NAME]
    python main.py train [--config CONFIG] [--resume]
    python main.py backtest [--model MODEL] [--start-date DATE] [--end-date DATE]
    python main.py papertrade [--model MODEL] [--duration DAYS]

Commands:
    get-data      Fetch market data with caching
    process-data  Data preprocessing and feature engineering
    tune          Hyperparameter optimization with Optuna
    train         Model training with ElegantRL SAC
    backtest      Historical performance evaluation
    papertrade    Live paper trading simulation
"""

import sys
from pathlib import Path
from typing import Optional

import click
import numpy as np
from loguru import logger
import optuna # Import optuna for type hinting

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from config.settings import Settings
from utils.logging import setup_logging


@click.group()
@click.option('--config', '-c', type=click.Path(exists=True), help='Configuration file path')
@click.option('--verbose', '-v', is_flag=True, help='Enable verbose logging')
@click.option('--log-file', type=click.Path(), help='Log file path')
@click.pass_context
def cli(ctx: click.Context, config: Optional[str], verbose: bool, log_file: Optional[str]):
    """FinRL Stock Trading Agent CLI"""
    # Ensure context object exists
    ctx.ensure_object(dict)
    
    # Load configuration
    settings = Settings(config_file=config)
    ctx.obj = {'settings': settings, 'logger': logger}
    import os
    logger.info(f"[CLI PID:{os.getpid()}] Initialized settings.data.tech_indicator_list: {settings.data.tech_indicator_list} (Length: {len(settings.data.tech_indicator_list)})")
    logger.info(f"[CLI PID:{os.getpid()}] Initialized settings.data.include_vix: {settings.data.include_vix}")
    logger.info(f"[CLI PID:{os.getpid()}] Initialized settings.data.vix_features: {settings.data.vix_features}")
    
    # Determine log file path for workers
    worker_log_file_path = log_file or settings.logging.file_path
    ctx.obj['worker_log_file_path'] = worker_log_file_path

    # Setup logging
    setup_logging(
        level=settings.logging.level or "INFO", # Use level from settings or default to INFO
        log_file=worker_log_file_path, # Use the determined worker_log_file_path
        worker_id="main", # Identify this as the main process log
        rotation=settings.logging.rotation,
        retention=settings.logging.retention,
        format_type=settings.logging.format_type or "detailed",
        enable_console=settings.logging.enable_console_main, # Use a specific setting for main console
        enable_file=True # Ensure file logging is enabled for main process
    )
    
    logger.info("FinRL Trading Agent CLI initialized")
    logger.debug(f"Configuration loaded from: {config or 'default'}")


@cli.command()
@click.option('--symbols', type=str, help='Comma-separated list of symbols')
@click.option('--start-date', type=click.DateTime(formats=['%Y-%m-%d']), help='Start date (YYYY-MM-DD)')
@click.option('--end-date', type=click.DateTime(formats=['%Y-%m-%d']), help='End date (YYYY-MM-DD)')
@click.option('--force-refresh', is_flag=True, help='Force refresh cached data')
@click.pass_context
def get_data(ctx: click.Context, symbols: Optional[str], start_date: Optional[str], 
             end_date: Optional[str], force_refresh: bool):
    """Fetch market data with caching"""
    from data.fetcher import DataFetcher
    
    settings = ctx.obj['settings']
    logger.info("Starting data fetching process")
    
    try:
        fetcher = DataFetcher()  # Removed settings argument
        
        # Use provided symbols or default from config
        target_symbols = symbols.split(',') if symbols else settings.data.symbols
        
        # Fetch data for each symbol
        for symbol in target_symbols:
            logger.info(f"Fetching data for {symbol}")
            data = fetcher.fetch_symbol_data(
                symbol=symbol,
                start_date=start_date or settings.data.train_start_date,
                end_date=end_date or settings.data.test_end_date,
                force_refresh=force_refresh
            )
            logger.success(f"Successfully fetched {len(data)} records for {symbol}")
        
        # Fetch VIX data
        logger.info("Fetching VIX data")
        vix_data = fetcher.fetch_vix_data(
            start_date=start_date or settings.data.train_start_date,
            end_date=end_date or settings.data.test_end_date,
            force_refresh=force_refresh
        )
        logger.success(f"Successfully fetched {len(vix_data)} VIX records")
        
        logger.success("Data fetching completed successfully")
        
    except Exception as e:
        logger.exception(f"Data fetching failed: {e}")
        logger.complete()  # Force flush logs
        sys.exit(1)


@cli.command()
@click.option('--output-dir', type=click.Path(), help='Output directory for processed data')
@click.pass_context
def process_data(ctx: click.Context, output_dir: Optional[str]):
    """Data preprocessing and feature engineering"""
    import pandas as pd
    from data.fetcher import DataFetcher
    from data.processor import DataProcessor
    
    settings = ctx.obj['settings']
    logger.info("Starting data processing")
    
    fetcher = DataFetcher()
    processor = DataProcessor(
        fetcher=fetcher,
        settings_obj=settings, # Pass the settings object
        tech_indicator_list=[tech.lower() for tech in settings.data.tech_indicator_list],
        vix_features=settings.data.vix_features,
        include_vix=settings.data.include_vix
    )
    
    # Get symbols and date range from settings
    symbols = settings.data.symbols
    start_date = settings.data.train_start_date
    end_date = settings.data.test_end_date
    
    all_stock_data = []
    failed_symbols = []
    
    # Fetch and process data for each symbol
    logger.info(f"Fetching and processing data for {len(symbols)} symbols...")
    for symbol in symbols:
        logger.info(f"Processing data for {symbol}")
        try:
            # Fetch data (will use cache if available and valid)
            raw_data = fetcher.fetch_symbol_data(
                symbol=symbol,
                start_date=start_date,
                end_date=end_date,
                force_refresh=False # Use cached data if available
            )
            
            if not raw_data.empty:
                # Create a fresh copy before processing to avoid SettingWithCopyWarning issues
                data_to_process = raw_data.copy()
                
                # Process stock data (add indicators, derived features, clean)
                processed_symbol_data = processor.process_stock_data(
                    data=data_to_process,
                    add_indicators=True,
                    normalize=False # Normalization will be done later if needed for training
                )
                all_stock_data.append(processed_symbol_data)
                logger.success(f"Successfully processed data for {symbol}")
            else:
                logger.warning(f"No data available for {symbol}, skipping processing.")
                failed_symbols.append(symbol)
                
        except Exception as e:
            logger.error(f"Failed to process data for {symbol}: {e}")
            failed_symbols.append(symbol)
            
    if failed_symbols:
        logger.warning(f"Skipped processing for failed symbols: {failed_symbols}")
    
    if not all_stock_data:
        logger.error("No stock data was successfully processed for any symbol.")
        sys.exit(1)
        
    # Concatenate data from all symbols
    combined_stock_data = pd.concat(all_stock_data, ignore_index=True)
    combined_stock_data = combined_stock_data.sort_values(['tic', 'date']).reset_index(drop=True)
    
    logger.info(f"Combined data for all symbols: {len(combined_stock_data)} records")
    
    # Fetch and process VIX data
    logger.info("Fetching and processing VIX data")
    try:
        raw_vix_data = fetcher.fetch_vix_data(
            start_date=start_date,
            end_date=end_date,
            force_refresh=False # Use cached data if available
        )
        
        processed_vix_data = processor.process_vix_data(
            vix_data=raw_vix_data,
            add_indicators=True
        )
        logger.success("Successfully processed VIX data")
    except Exception as e:
        logger.error(f"Failed to process VIX data: {e}")
        processed_vix_data = pd.DataFrame() # Use empty DataFrame if VIX processing fails
        
    # Merge stock data with VIX data
    logger.info("Merging stock data with VIX data")
    final_processed_data = processor.merge_with_vix(
        stock_data=combined_stock_data,
        vix_data=processed_vix_data
    )
    logger.success(f"Successfully merged data: {len(final_processed_data)} records")

    # Rename VIX columns in the final merged data to match tech_indicator_list expectations
    if not processed_vix_data.empty and not final_processed_data.empty:
        vix_rename_map_for_final_df = {
            # VIX columns are now already in lowercase from processor
        # No mapping needed as all columns use consistent lowercase naming
            # Add other VIX columns if they are generated by _add_vix_indicators and needed
        }
        
        actual_renames_in_final = {k: v for k, v in vix_rename_map_for_final_df.items() if k in final_processed_data.columns}
        if actual_renames_in_final:
            final_processed_data.rename(columns=actual_renames_in_final, inplace=True)
            logger.info(f"Renamed VIX columns in final processed data: {actual_renames_in_final}")
    
    # Rename 'symbol' to 'tic' for environment compatibility before saving
    if 'symbol' in final_processed_data.columns and 'tic' not in final_processed_data.columns:
        logger.info("Renaming 'symbol' column to 'tic' for environment compatibility.")
        final_processed_data.rename(columns={'symbol': 'tic'}, inplace=True)
    
    # Ensure we have the 'tic' column
    if 'tic' not in final_processed_data.columns:
        logger.error("Data integrity check failed. 'tic' column missing after processing.")
        raise ValueError("Processed data is missing 'tic' column")
    
    # Define output path
    output_directory = Path(output_dir or settings.data.processed_dir)
    output_directory.mkdir(parents=True, exist_ok=True)
    output_file = output_directory / "processed_data.csv"
    
    # Save processed data
    logger.info(f"Saving processed data to {output_file}")
    final_processed_data.to_csv(output_file, index=False)
    logger.success("Processed data saved successfully")
    
    # Log data summary
    logger.info(f"Final processed data summary:")
    logger.info(f"  - Total records: {len(final_processed_data)}")
    logger.info(f"  - Unique symbols: {final_processed_data['tic'].nunique()}")
    logger.info(f"  - Date range: {final_processed_data['date'].min()} to {final_processed_data['date'].max()}")
    logger.info(f"  - Columns: {len(final_processed_data.columns)}")
    logger.info(f"  - Sample symbols: {final_processed_data['tic'].unique()[:5].tolist()}")
    
    logger.success("Data processing completed successfully")


@cli.command()
@click.option('--trials', type=int, help='Number of optimization trials')
@click.option('--study-name', type=str, help='Optuna study name')
@click.option('--resume', is_flag=True, help='Resume existing study')
@click.pass_context
def tune(ctx: click.Context, trials: Optional[int], study_name: Optional[str], resume: bool):
    """Hyperparameter optimization with Optuna"""
    from models.optimization import HyperparameterOptimizer, OptimizationConfig, SingleObjective
    from models.training import TrainingConfig
    from models.sac_agent import SACAgent
    from data.processor import DataProcessor
    from trading.asymmetric_env import AsymmetricTradingEnv

    import pandas as pd
    import optuna
    
    settings = ctx.obj['settings']
    logger.info("Starting hyperparameter tuning")
    
    try:
        # Load and process data
        try:
            processed_file_path = Path(settings.project_root) / settings.data.processed_dir / settings.data.processed_file_name
            processed_data_df = pd.read_csv(processed_file_path)
            # Keep 'date' column as lowercase for consistency with DataProcessor
            
            # Initialize DataFetcher for the train command
            from data.fetcher import DataFetcher # Ensure DataFetcher is imported
            fetcher = DataFetcher() # Create an instance of DataFetcher
            data_processor = DataProcessor(
                fetcher=fetcher, # Pass the fetcher instance
                settings_obj=settings, # Pass the settings object
                tech_indicator_list=[tech.lower() for tech in settings.data.tech_indicator_list],
                vix_features=settings.data.vix_features,
                include_vix=settings.data.include_vix
            )
            processed_for_env_df = data_processor.process_stock_data(processed_data_df.copy())

            logger.info(f"--- Quick Stats for processed_for_env_df (after DataProcessor.process_stock_data) ---")
            logger.info(f"Shape: {processed_for_env_df.shape}")
            if not processed_for_env_df.empty:
                logger.info(f"Columns: {processed_for_env_df.columns.tolist()}")
                nan_counts = processed_for_env_df.isnull().sum()
                inf_counts = processed_for_env_df.apply(lambda x: np.isinf(x).sum() if pd.api.types.is_numeric_dtype(x) else 0)
                logger.info(f"NaN counts per column (if any):\n{nan_counts[nan_counts > 0]}")
                logger.info(f"Inf counts per column (if any):\n{inf_counts[inf_counts > 0]}")
                if 'date' in processed_for_env_df.columns:
                    logger.info(f"Date range in processed_for_env_df ('date'): {processed_for_env_df['date'].min()} to {processed_for_env_df['date'].max()}")
                if 'tic' in processed_for_env_df.columns:
                    logger.info(f"Unique 'tic' in processed_for_env_df: {processed_for_env_df['tic'].nunique()}")
            else:
                logger.warning("processed_for_env_df is empty.")
            logger.info(f"--- End Quick Stats for processed_for_env_df ---")

            # Verify 'tic' column exists (should be created by process-data command)
            if processed_for_env_df.empty or 'tic' not in processed_for_env_df.columns:
                logger.error(f"Data integrity check failed. 'tic' column missing or data empty. Columns: {processed_for_env_df.columns.tolist() if not processed_for_env_df.empty else 'N/A (empty df)'}")
                logger.error("Please run 'python main.py process-data' first to ensure data is properly processed with 'tic' column.")
                raise ValueError("Processed data is empty or missing 'tic' column")

            # Process and merge VIX indicators
            # VIX indicators should already be merged and correctly named from the process-data step.
            # The processed_for_env_df loaded from processed_data.csv is expected to contain them.
            logger.info("Assuming VIX indicators are pre-merged and correctly named in the loaded data.")

            # Split data for training and validation
            # Using 'date' (lowercase) as standardized by DataProcessor
            unique_dates = sorted(processed_for_env_df['date'].unique())
            split_idx = int(len(unique_dates) * 0.8)  # 80% for training
            train_dates = unique_dates[:split_idx]
            val_dates = unique_dates[split_idx:]
            
            train_df = processed_for_env_df[processed_for_env_df['date'].isin(train_dates)].reset_index(drop=True)
            val_df = processed_for_env_df[processed_for_env_df['date'].isin(val_dates)].reset_index(drop=True)

            # Rename columns to lowercase for FinRL compatibility
            column_rename_map = {col: col.lower() for col in ['Open', 'High', 'Low', 'Close', 'Volume'] if col in train_df.columns}
            if column_rename_map:
                logger.info(f"Renaming columns to lowercase for FinRL: {column_rename_map}")
                train_df.rename(columns=column_rename_map, inplace=True)
                val_df.rename(columns=column_rename_map, inplace=True)
            
            logger.info(f"Training data: {len(train_df)} records, Validation data: {len(val_df)} records")
            
            # Transform data format for FinRL environment
            # FinRL expects data in a specific format where df.loc[day, :] returns data for all stocks at that day
            def prepare_finrl_data(df):
                """Transform data from long format to FinRL-compatible format.
                
                FinRL expects the DataFrame to be structured so that when you do df.loc[day, :],
                you get a row with data for all stocks for that day.
                """
                # Ensure date column is datetime and handle timezone-aware datetimes
                if 'date' in df.columns:
                    # Convert timezone-aware datetime to UTC then remove timezone
                    df['date'] = pd.to_datetime(df['date'], utc=True).dt.tz_localize(None)
                
                # Rename columns to match FinRL expectations (lowercase)
                # Keep all technical indicators and just convert to lowercase
                column_mapping = {
                    'symbol': 'tic',
                    'Open': 'open',
                    'High': 'high', 
                    'Low': 'low',
                    'Close': 'close',
                    'Volume': 'volume'
                }
                
                # Add all technical indicators from settings to mapping (convert to lowercase)
                # Fix: Check actual data columns instead of settings names since pandas_ta generates uppercase
                for col in df.columns:
                    if col in settings.data.tech_indicator_list:
                        column_mapping[col] = col.lower()
                
                # Apply column renaming
                df = df.rename(columns=column_mapping)
                
                # Sort by date and tic
                df = df.sort_values(['date', 'tic']).reset_index(drop=True)
                
                # Create a proper index for FinRL
                # FinRL uses integer index where each index represents a time step
                df['day'] = df['date'].factorize()[0]
                
                # Set the day as index for FinRL's df.loc[day, :] operation
                df = df.set_index('day')
                
                return df
            
            logger.info("Transforming data format for FinRL environment compatibility")
            train_df = prepare_finrl_data(train_df)
            val_df = prepare_finrl_data(val_df)

            logger.info(f"Train_df columns before deduplication (showing only duplicated ones): {train_df.columns[train_df.columns.duplicated(keep=False)].unique().tolist()}")
            train_df = train_df.loc[:, ~train_df.columns.duplicated(keep='first')]
            logger.info(f"Train_df columns after deduplication: {len(train_df.columns)}")

            logger.info(f"Val_df columns before deduplication (showing only duplicated ones): {val_df.columns[val_df.columns.duplicated(keep=False)].unique().tolist()}")
            val_df = val_df.loc[:, ~val_df.columns.duplicated(keep='first')]
            logger.info(f"Val_df columns after deduplication: {len(val_df.columns)}")
            
            logger.info(f"Transformed training data: {len(train_df)} records, Validation data: {len(val_df)} records")
            
        except FileNotFoundError:
            logger.error(f"Processed data file not found at {settings.data.processed_file_path}. Please run data processing first.")
            sys.exit(1)
        except Exception as e:
            logger.error(f"Data loading failed: {e}")
            sys.exit(1)
        
        # Ensure 'date' is datetime and set as index for train_df and val_df
        # This should ideally be handled robustly in DataProcessor, but adding here for explicit control before env creation
        for current_df, df_name in [(train_df, "training"), (val_df, "validation")]:
            if 'date' in current_df.columns:
                # Ensure 'date' column is not already the index
                if current_df.index.name != 'date':
                    current_df['date'] = pd.to_datetime(current_df['date'])
                    current_df.set_index('date', inplace=True)
                    logger.info(f"Set 'date' as index for {df_name} data.")
                else:
                    logger.info(f"'date' is already the index for {df_name} data.")
            else:
                logger.error(f"'date' column not found in {df_name} DataFrame before setting index for environment. This is critical.")
                # Consider sys.exit(1) or raising an exception if this is fatal

        # --- Debugging: Process tech indicators directly on train_df and val_df --- 
        logger.info("Applying NaN fill and type check for technical indicators in training data.")
        for col in settings.data.tech_indicator_list:
            if col in train_df.columns:
                original_nan_count = train_df[col].isnull().sum()
                train_df[col] = pd.to_numeric(train_df[col], errors='coerce')
                coercion_nan_count = train_df[col].isnull().sum()
                if coercion_nan_count > original_nan_count:
                    logger.warning(f"Tech indicator '{col}' in training data had values coerced to NaN by pd.to_numeric.")
                if train_df[col].isnull().all() and original_nan_count < len(train_df[col]):
                    logger.warning(f"Tech indicator '{col}' in training data became all NaNs after pd.to_numeric. Original NaN count: {original_nan_count}, Length: {len(train_df[col])}")
                train_df[col].fillna(0, inplace=True)
            else:
                logger.error(f"CRITICAL: Technical indicator '{col}' from settings.data.tech_indicator_list NOT FOUND in training DataFrame. Adding as zeros.")
                train_df[col] = 0.0
        
        logger.info("Applying NaN fill and type check for technical indicators in validation data.")
        for col in settings.data.tech_indicator_list:
            if col in val_df.columns:
                original_nan_count = val_df[col].isnull().sum()
                val_df[col] = pd.to_numeric(val_df[col], errors='coerce')
                coercion_nan_count = val_df[col].isnull().sum()
                if coercion_nan_count > original_nan_count:
                    logger.warning(f"Tech indicator '{col}' in validation data had values coerced to NaN by pd.to_numeric.")
                if val_df[col].isnull().all() and original_nan_count < len(val_df[col]):
                     logger.warning(f"Tech indicator '{col}' in validation data became all NaNs after pd.to_numeric. Original NaN count: {original_nan_count}, Length: {len(val_df[col])}")
                val_df[col].fillna(0, inplace=True)
            else:
                logger.error(f"CRITICAL: Technical indicator '{col}' from settings.data.tech_indicator_list NOT FOUND in validation DataFrame. Adding as zeros.")
                val_df[col] = 0.0
        # --- End Debugging ---   

            # Create training environment
        try:
            from trading.asymmetric_env import AsymmetricTradingEnv
            from strategies.asymmetric_strategy import AsymmetricConfig
            
            stock_dim = len(train_df['tic'].unique())
            # Base state space: balance + stock holdings + technical indicators
            base_state_space = 1 + 2 * stock_dim + len(settings.data.tech_indicator_list) * stock_dim
            # Add asymmetric features: 5 features per stock (asymmetry_score, rsi, bb_position, momentum, volatility)
            asymmetric_features = 5 * stock_dim
            state_space_calculated_in_main = base_state_space + asymmetric_features # This is the enhanced version
            action_space = stock_dim
            
            # --- Determine state_space and enhanced_state_input for the environment --- 
            # This flag should ideally come from settings.py (e.g., settings.env.use_enhanced_state)
            USE_ENHANCED_STATE_FOR_TRAIN_ENV = True 
            
            if USE_ENHANCED_STATE_FOR_TRAIN_ENV:
                actual_state_space_for_train_env = state_space_calculated_in_main
            else:
                actual_state_space_for_train_env = base_state_space
            # --- End determination --- 

            # Create asymmetric strategy configuration
            asymmetric_config = AsymmetricConfig(
                symbols=train_df['tic'].unique().tolist(),
                target_upside_downside_ratio=settings.asymmetric.target_upside_downside_ratio,
                momentum_threshold=settings.asymmetric.momentum_threshold,
                mean_reversion_threshold=settings.asymmetric.mean_reversion_threshold,
                volatility_lookback=settings.asymmetric.volatility_lookback,
                rsi_period=settings.asymmetric.rsi_period,
                rsi_oversold=settings.asymmetric.rsi_oversold,
                rsi_overbought=settings.asymmetric.rsi_overbought,
                fast_ma_period=settings.asymmetric.fast_ma_period,
                slow_ma_period=settings.asymmetric.slow_ma_period,
                bb_period=settings.asymmetric.bb_period,
                bb_std=settings.asymmetric.bb_std,
                signal_threshold=settings.asymmetric.signal_threshold
            )

            env_config = {
                'df': train_df,
                'stock_dim': stock_dim,
                'hmax': settings.env.hmax,
                'initial_amount': settings.env.initial_amount,
                'num_stock_shares': [0] * stock_dim,
                'buy_cost_pct': [settings.env.transaction_cost_pct] * stock_dim,
                'sell_cost_pct': [settings.env.transaction_cost_pct] * stock_dim,
                'state_space': actual_state_space_for_train_env, # Pass the correctly determined state_space
                'action_space': action_space,
                'tech_indicator_list': settings.data.tech_indicator_list,
                'reward_scaling': settings.env.reward_scaling,
                'asymmetric_config': asymmetric_config,
                'enhanced_state_input': USE_ENHANCED_STATE_FOR_TRAIN_ENV, # Pass the flag consistently
                'log_file_path': ctx.obj['worker_log_file_path'],
                'log_level': settings.logging.level or 'INFO' 
            }
            train_env = AsymmetricTradingEnv(**env_config)
            
            # Create validation environment
            # Recalculate stock_dim and related spaces for validation set if symbols differ, though typically they should align
            val_stock_dim = len(val_df['tic'].unique())
            # Base state space: balance + stock holdings + technical indicators
            val_base_state_space = 1 + 2 * val_stock_dim + len(settings.data.tech_indicator_list) * val_stock_dim
            # Add asymmetric features: 5 features per stock (asymmetry_score, rsi, bb_position, momentum, volatility)
            val_asymmetric_features = 5 * val_stock_dim
            val_state_space_enhanced = val_base_state_space + val_asymmetric_features # This is the enhanced version for validation
            val_action_space = val_stock_dim

            # --- Determine state_space and enhanced_state_input for the validation environment --- 
            # This flag should ideally come from settings.py and match the training env's setting for consistency in agent architecture
            USE_ENHANCED_STATE_FOR_EVAL_ENV = True 
            
            if USE_ENHANCED_STATE_FOR_EVAL_ENV:
                actual_state_space_for_eval_env = val_state_space_enhanced
            else:
                actual_state_space_for_eval_env = val_base_state_space
            # --- End determination --- 

            eval_env_config = env_config.copy() # Copies base config, including worker log path etc.
            eval_env_config['df'] = val_df
            eval_env_config['stock_dim'] = val_stock_dim
            eval_env_config['num_stock_shares'] = [0] * val_stock_dim
            eval_env_config['state_space'] = actual_state_space_for_eval_env # Pass the correctly determined state_space
            eval_env_config['action_space'] = val_action_space
            eval_env_config['enhanced_state_input'] = USE_ENHANCED_STATE_FOR_EVAL_ENV # Pass the flag consistently
            # Ensure asymmetric_config for eval_env uses symbols from val_df
            eval_asymmetric_config = asymmetric_config.model_copy(deep=True) # Create a copy to modify symbols
            eval_asymmetric_config.symbols = val_df['tic'].unique().tolist()
            eval_env_config['asymmetric_config'] = eval_asymmetric_config
            eval_env = AsymmetricTradingEnv(**eval_env_config)
            
            # Get environment dimensions
            state_dim = train_env.observation_space.shape[0]
            action_dim = train_env.action_space.shape[0]
            
            logger.info(f"Environment created: state_dim={state_dim}, action_dim={action_dim}")
            
        except Exception as e:
            logger.error(f"Environment creation failed: {e}")
            sys.exit(1)
        
        # Create optimization configuration
        optimization_config = OptimizationConfig(
            n_trials=trials or getattr(settings.optuna, 'n_trials', 100),
            timeout=getattr(settings.optuna, 'timeout', None),
            study_name=study_name or settings.optuna.study_name,
            storage=settings.optuna.storage_url,
            load_if_exists=resume,
            pruning_warmup_steps=settings.optuna.pruner_n_startup_trials,
            pruning_interval_steps=settings.optuna.pruner_n_warmup_steps
        )
        
        # Create training configuration for optimization
        training_config = TrainingConfig(
            total_timesteps=getattr(settings.optuna, 'max_timesteps_per_trial', 10000),
            eval_freq=getattr(settings.optuna, 'max_timesteps_per_trial', 10000) // 4,
            save_freq=getattr(settings.optuna, 'max_timesteps_per_trial', 10000) // 2,
            log_interval=100
        )
        
        # Create objective function
        objective_function = SingleObjective(
            train_env=train_env,
            eval_env=eval_env,
            state_dim=state_dim,
            action_dim=action_dim,
            training_config=training_config
        )
        
        # Create optimizer and run optimization
        optimizer = HyperparameterOptimizer(config=optimization_config)
        
        results = optimizer.optimize(
            objective=objective_function,
            save_dir=settings.sac.checkpoint_dir
        )
        
        if not results['success']:
            raise Exception(f"Optimization failed: {results.get('error', 'unknown error')}")
            
        best_params = results['best_params']
        
        logger.success(f"Hyperparameter tuning completed. Best params: {best_params}")
        
        # Save best parameters to file
        import json
        best_params_file = Path(settings.sac.checkpoint_dir) / "best_hyperparameters.json"
        best_params_file.parent.mkdir(parents=True, exist_ok=True)
        with open(best_params_file, 'w') as f:
            json.dump(best_params, f, indent=2)
        
        logger.info(f"Best hyperparameters saved to: {best_params_file}")
        
    except Exception as e:
        logger.exception(f"Hyperparameter tuning failed: {e}")
        sys.exit(1)


@cli.command()
@click.option('--config-override', type=str, help='JSON string to override config')
@click.option('--resume', is_flag=True, help='Resume training from checkpoint')
@click.option('--use-best-params', is_flag=True, help='Use best hyperparameters from tuning')
@click.pass_context
def train(ctx: click.Context, config_override: Optional[str], resume: bool, use_best_params: bool):
    """Train the SAC agent"""
    from models.sac_agent import SACAgent
    from models.training import TrainingConfig
    from data.processor import DataProcessor
    from trading.asymmetric_env import AsymmetricTradingEnv

    import pandas as pd
    import json
    import numpy as np
    
    settings = ctx.obj['settings']
    logger.info("Starting SAC agent training")

    try:
        np.seterr(all='raise', divide='raise', over='raise', under='raise', invalid='raise')
        logger.info("NumPy error reporting set to 'raise' for all floating point issues.")
    except NameError:
        logger.error("NumPy (np) not defined. Cannot set error reporting. Ensure 'import numpy as np' is at the function start.")
    except Exception as e:
        logger.error(f"Failed to set NumPy error reporting: {e}")
    
    try:
        # Load best hyperparameters if requested
        if use_best_params:
            best_params_file = Path(settings.sac.checkpoint_dir) / "best_hyperparameters.json"
            if best_params_file.exists():
                with open(best_params_file, 'r') as f:
                    best_params = json.load(f)
                logger.info(f"Using best hyperparameters: {best_params}")
            else:
                logger.warning("Best hyperparameters file not found. Using default settings.")
                best_params = {}
        else:
            best_params = {}
        
        # Apply config override if provided
        if config_override:
            try:
                override_params = json.loads(config_override)
                best_params.update(override_params)
                logger.info(f"Applied config override: {override_params}")
            except json.JSONDecodeError as e:
                logger.error(f"Invalid JSON in config override: {e}")
                sys.exit(1)
        
        # Load and process data
        try:
            processed_file_path = Path(settings.project_root) / settings.data.processed_dir / settings.data.processed_file_name
            processed_data_df = pd.read_csv(processed_file_path)
            # Keep 'date' column as lowercase for consistency with DataProcessor
            
            # Initialize DataFetcher for the train command
            from data.fetcher import DataFetcher # Ensure DataFetcher is imported
            fetcher = DataFetcher() # Create an instance of DataFetcher
            data_processor = DataProcessor(
                fetcher=fetcher, # Pass the fetcher instance
                settings_obj=settings, # Pass the settings object
                tech_indicator_list=[tech.lower() for tech in settings.data.tech_indicator_list],
                vix_features=settings.data.vix_features,
                include_vix=settings.data.include_vix
            )
            processed_for_env_df = data_processor.process_stock_data(processed_data_df.copy())

            logger.info(f"--- Quick Stats for processed_for_env_df (after DataProcessor.process_stock_data) ---")
            logger.info(f"Shape: {processed_for_env_df.shape}")
            if not processed_for_env_df.empty:
                logger.info(f"Columns: {processed_for_env_df.columns.tolist()}")
                nan_counts = processed_for_env_df.isnull().sum()
                inf_counts = processed_for_env_df.apply(lambda x: np.isinf(x).sum() if pd.api.types.is_numeric_dtype(x) else 0)
                logger.info(f"NaN counts per column (if any):\n{nan_counts[nan_counts > 0]}")
                logger.info(f"Inf counts per column (if any):\n{inf_counts[inf_counts > 0]}")
                if 'date' in processed_for_env_df.columns:
                    logger.info(f"Date range in processed_for_env_df ('date'): {processed_for_env_df['date'].min()} to {processed_for_env_df['date'].max()}")
                if 'tic' in processed_for_env_df.columns:
                    logger.info(f"Unique 'tic' in processed_for_env_df: {processed_for_env_df['tic'].nunique()}")
            else:
                logger.warning("processed_for_env_df is empty.")
            logger.info(f"--- End Quick Stats for processed_for_env_df ---")

            # Verify 'tic' column exists (should be created by process-data command)
            if processed_for_env_df.empty or 'tic' not in processed_for_env_df.columns:
                logger.error(f"Data integrity check failed. 'tic' column missing or data empty. Columns: {processed_for_env_df.columns.tolist() if not processed_for_env_df.empty else 'N/A (empty df)'}")
                logger.error("Please run 'python main.py process-data' first to ensure data is properly processed with 'tic' column.")
                raise ValueError("Processed data is empty or missing 'tic' column")

            # Process and merge VIX indicators
            # VIX indicators should already be merged and correctly named from the process-data step.
            # The processed_for_env_df loaded from processed_data.csv is expected to contain them.
            logger.info("Assuming VIX indicators are pre-merged and correctly named in the loaded data.")

            # Split data for training and validation
            # Using 'date' (lowercase) as standardized by DataProcessor
            unique_dates = sorted(processed_for_env_df['date'].unique())
            split_idx = int(len(unique_dates) * 0.8)  # 80% for training
            train_dates = unique_dates[:split_idx]
            val_dates = unique_dates[split_idx:]
            
            train_df = processed_for_env_df[processed_for_env_df['date'].isin(train_dates)].reset_index(drop=True)
            val_df = processed_for_env_df[processed_for_env_df['date'].isin(val_dates)].reset_index(drop=True)

            # Rename columns to lowercase for FinRL compatibility
            column_rename_map = {col: col.lower() for col in ['Open', 'High', 'Low', 'Close', 'Volume'] if col in train_df.columns}
            if column_rename_map:
                logger.info(f"Renaming columns to lowercase for FinRL: {column_rename_map}")
                train_df.rename(columns=column_rename_map, inplace=True)
                val_df.rename(columns=column_rename_map, inplace=True)
            
            logger.info(f"Training data: {len(train_df)} records, Validation data: {len(val_df)} records")
            
            # Transform data format for FinRL environment
            # FinRL expects data in a specific format where df.loc[day, :] returns data for all stocks at that day
            def prepare_finrl_data(df):
                """Transform data from long format to FinRL-compatible format.
                
                FinRL expects the DataFrame to be structured so that when you do df.loc[day, :],
                you get a row with data for all stocks for that day.
                """
                # Ensure date column is datetime and handle timezone-aware datetimes
                if 'date' in df.columns:
                    # Convert timezone-aware datetime to UTC then remove timezone
                    df['date'] = pd.to_datetime(df['date'], utc=True).dt.tz_localize(None)
                
                # Rename columns to match FinRL expectations (lowercase)
                # Keep all technical indicators and just convert to lowercase
                column_mapping = {
                    'symbol': 'tic',
                    'Open': 'open',
                    'High': 'high', 
                    'Low': 'low',
                    'Close': 'close',
                    'Volume': 'volume'
                }
                
                # Add all technical indicators from settings to mapping (convert to lowercase)
                # Fix: Check actual data columns instead of settings names since pandas_ta generates uppercase
                for col in df.columns:
                    if col in settings.data.tech_indicator_list:
                        column_mapping[col] = col.lower()
                
                # Apply column renaming
                df = df.rename(columns=column_mapping)
                
                # Sort by date and tic
                df = df.sort_values(['date', 'tic']).reset_index(drop=True)
                
                # Create a proper index for FinRL
                # FinRL uses integer index where each index represents a time step
                df['day'] = df['date'].factorize()[0]
                
                # Set the day as index for FinRL's df.loc[day, :] operation
                df = df.set_index('day')
                
                return df
            
            logger.info("Transforming data format for FinRL environment compatibility")
            train_df = prepare_finrl_data(train_df)
            val_df = prepare_finrl_data(val_df)

            logger.info(f"Train_df columns before deduplication (showing only duplicated ones): {train_df.columns[train_df.columns.duplicated(keep=False)].unique().tolist()}")
            train_df = train_df.loc[:, ~train_df.columns.duplicated(keep='first')]
            logger.info(f"Train_df columns after deduplication: {len(train_df.columns)}")

            logger.info(f"Val_df columns before deduplication (showing only duplicated ones): {val_df.columns[val_df.columns.duplicated(keep=False)].unique().tolist()}")
            val_df = val_df.loc[:, ~val_df.columns.duplicated(keep='first')]
            logger.info(f"Val_df columns after deduplication: {len(val_df.columns)}")
            
            logger.info(f"Transformed training data: {len(train_df)} records, Validation data: {len(val_df)} records")
            
        except FileNotFoundError:
            logger.error(f"Processed data file not found at {settings.data.processed_file_path}. Please run data processing first.")
            sys.exit(1)
        except Exception as e:
            logger.exception(f"Data loading failed: {e}")
            sys.exit(1)
        
        train_data_for_env = train_df.reset_index(drop=True)
        validation_data_for_env = val_df.reset_index(drop=True)

        # Ensure 'date' is datetime (it is converted in standardize_columns, but good to be explicit if needed)
        # train_data_for_env['date'] = pd.to_datetime(train_data_for_env['date'])
        # validation_data_for_env['date'] = pd.to_datetime(validation_data_for_env['date'])

        # The DataFrames train_data_for_env and validation_data_for_env are now correctly
        # indexed by 'day' as a result of the prepare_finrl_data function.
        # The following set_index('date') calls were causing issues and have been removed.
        logger.info("Skipping set_index('date') as 'day' index is already set by prepare_finrl_data.")

            # --- Debugging: Process tech indicators before passing to Env --- 
        logger.info("Applying NaN fill and type check for technical indicators in training data.")
        for col in settings.data.tech_indicator_list:
            # Check for lowercase version since prepare_finrl_data converts to lowercase
            col_lower = col.lower()
            if col_lower in train_data_for_env.columns:
                column_data_to_convert = train_data_for_env[col_lower]
                if isinstance(column_data_to_convert, pd.DataFrame):
                    logger.warning(f"Column '{col_lower}' is a DataFrame, likely due to duplicate names. Selecting first column for numeric conversion and summing NaNs across all its columns.")
                    original_nan_count = column_data_to_convert.isnull().sum().sum() # Sum of NaNs across all columns in the DataFrame slice
                    column_data_to_convert = column_data_to_convert.iloc[:, 0] # Select first column for conversion
                else:
                    original_nan_count = column_data_to_convert.isnull().sum() # Sum of NaNs for the Series
                
                train_data_for_env[col_lower] = pd.to_numeric(column_data_to_convert, errors='coerce')
                data_for_coercion_check_train = train_data_for_env[col_lower]
                if isinstance(data_for_coercion_check_train, pd.DataFrame):
                    logger.warning(f"Column '{col_lower}' in training data is a DataFrame when calculating coercion_nan_count. Summing NaNs across its columns.")
                    coercion_nan_count = data_for_coercion_check_train.isnull().sum().sum()
                else:
                    coercion_nan_count = data_for_coercion_check_train.isnull().sum()
                
                if coercion_nan_count > original_nan_count:
                    logger.warning(f"Tech indicator '{col_lower}' in training data had values coerced to NaN by pd.to_numeric.")
                if train_data_for_env[col_lower].isnull().all() and original_nan_count < len(train_data_for_env[col_lower]):
                    logger.warning(f"Tech indicator '{col_lower}' in training data became all NaNs after pd.to_numeric. Original NaN count: {original_nan_count}, Length: {len(train_data_for_env[col_lower])}")
                train_data_for_env[col_lower] = train_data_for_env[col_lower].fillna(0)
            else:
                logger.error(f"CRITICAL: Technical indicator '{col}' (lowercase: '{col_lower}') from settings.data.tech_indicator_list NOT FOUND in training DataFrame. Adding as zeros.")
                train_data_for_env[col_lower] = 0.0
        
        logger.info("Applying NaN fill and type check for technical indicators in validation data.")
        for col in settings.data.tech_indicator_list:
            # Check for lowercase version since prepare_finrl_data converts to lowercase
            col_lower = col.lower()
            if col_lower in validation_data_for_env.columns:
                column_data_to_convert_val = validation_data_for_env[col_lower]
                if isinstance(column_data_to_convert_val, pd.DataFrame):
                    logger.warning(f"Column '{col_lower}' in validation data is a DataFrame, likely due to duplicate names. Selecting first column for numeric conversion and summing NaNs across all its columns.")
                    original_nan_count = column_data_to_convert_val.isnull().sum().sum() # Sum of NaNs across all columns in the DataFrame slice
                    column_data_to_convert_val = column_data_to_convert_val.iloc[:, 0] # Select first column for conversion
                else:
                    original_nan_count = column_data_to_convert_val.isnull().sum() # Sum of NaNs for the Series
                
                validation_data_for_env[col_lower] = pd.to_numeric(column_data_to_convert_val, errors='coerce')
                data_for_coercion_check_val = validation_data_for_env[col_lower]
                if isinstance(data_for_coercion_check_val, pd.DataFrame):
                    logger.warning(f"Column '{col_lower}' in validation data is a DataFrame when calculating coercion_nan_count. Summing NaNs across its columns.")
                    coercion_nan_count = data_for_coercion_check_val.isnull().sum().sum()
                else:
                    coercion_nan_count = data_for_coercion_check_val.isnull().sum()
                
                if coercion_nan_count > original_nan_count:
                    logger.warning(f"Tech indicator '{col_lower}' in validation data had values coerced to NaN by pd.to_numeric.")
                if validation_data_for_env[col_lower].isnull().all() and original_nan_count < len(validation_data_for_env[col_lower]):
                        logger.warning(f"Tech indicator '{col_lower}' in validation data became all NaNs after pd.to_numeric. Original NaN count: {original_nan_count}, Length: {len(validation_data_for_env[col_lower])}")
                validation_data_for_env[col_lower] = validation_data_for_env[col_lower].fillna(0)
            else:
                logger.error(f"CRITICAL: Technical indicator '{col}' (lowercase: '{col_lower}') from settings.data.tech_indicator_list NOT FOUND in validation DataFrame. Adding as zeros.")
                validation_data_for_env[col_lower] = 0.0
        # --- End Debugging --- 

        # Create training environment with asymmetric strategy integration
        try:
            from trading.asymmetric_env import AsymmetricTradingEnv
            from strategies.asymmetric_strategy import AsymmetricConfig
            
            stock_dim = len(train_df['tic'].unique())
            # Use a large initial state space estimate - will be corrected after environment creation
            initial_state_space = 1000  # Large enough to accommodate any state size
            action_space = stock_dim
            
            # Create asymmetric strategy configuration
            asymmetric_config = AsymmetricConfig(
                symbols=train_df['tic'].unique().tolist(),
                target_upside_downside_ratio=settings.asymmetric.target_upside_downside_ratio,
                momentum_threshold=settings.asymmetric.momentum_threshold,
                mean_reversion_threshold=settings.asymmetric.mean_reversion_threshold,
                volatility_lookback=settings.asymmetric.volatility_lookback,
                rsi_period=settings.asymmetric.rsi_period,
                rsi_oversold=settings.asymmetric.rsi_oversold,
                rsi_overbought=settings.asymmetric.rsi_overbought,
                fast_ma_period=settings.asymmetric.fast_ma_period,
                slow_ma_period=settings.asymmetric.slow_ma_period,
                bb_period=settings.asymmetric.bb_period,
                bb_std=settings.asymmetric.bb_std,
                signal_threshold=settings.asymmetric.signal_threshold
            )

            env_config = {
                'df': train_df,
                'stock_dim': stock_dim,
                'hmax': settings.env.hmax,
                'initial_amount': settings.env.initial_amount,
                'num_stock_shares': [0] * stock_dim,
                'buy_cost_pct': [settings.env.transaction_cost_pct] * stock_dim,
                'sell_cost_pct': [settings.env.transaction_cost_pct] * stock_dim,
                'state_space': 0, # Placeholder, will be calculated inside AsymmetricTradingEnv
                'action_space': action_space,
                'tech_indicator_list': settings.data.tech_indicator_list,
                'reward_scaling': settings.env.reward_scaling,
                'asymmetric_config': asymmetric_config,
                'enhanced_state_input': True,
                'log_file_path': ctx.obj['worker_log_file_path'],
                'log_level': settings.logging.level or 'INFO' 
            }
            logger.info(f"About to create AsymmetricTradingEnv with config: stock_dim={stock_dim}, action_space={action_space}, df_shape={train_df.shape}")
            train_env = AsymmetricTradingEnv(**env_config)
            logger.info("AsymmetricTradingEnv created successfully")
            
            # Get actual state dimensions from the environment after creation
            logger.info("About to reset training environment")
            train_env.reset()
            logger.info("Training environment reset successfully")
            logger.info("About to get state from training environment")
            actual_state = train_env._get_state()
            actual_state_dim = len(actual_state)
            logger.info(f"Got actual state from training environment: dim={actual_state_dim}")
            
            # The observation_space and state_space are now correctly set within AsymmetricTradingEnv __init__
            # We just need to get the correct state_dim from the env for the agent
            actual_state_dim = train_env.observation_space.shape[0]
            logger.info(f"Train_env actual_state_dim after init: {actual_state_dim}")
            
            # Create validation environment with the same actual state dimension
            val_stock_dim = len(val_df['tic'].unique())
            val_action_space = val_stock_dim

            val_env_config = env_config.copy()
            val_env_config['df'] = val_df
            val_env_config['stock_dim'] = val_stock_dim
            val_env_config['num_stock_shares'] = [0] * val_stock_dim
            val_env_config['state_space'] = 0 # Placeholder, will be calculated inside AsymmetricTradingEnv
            val_env_config['action_space'] = val_action_space
            # Ensure enhanced_state_input is also set for val_env if it was for train_env
            val_env_config['enhanced_state_input'] = True # Enable enhanced state with asymmetric features
            logger.info(f"About to create validation AsymmetricTradingEnv with config: stock_dim={val_stock_dim}, action_space={val_action_space}")
            val_env = AsymmetricTradingEnv(**val_env_config)
            logger.info("Validation AsymmetricTradingEnv created successfully")
            
            # Update validation environment observation space as well
            logger.info("About to reset validation environment")
            val_env.reset()
            logger.info("Validation environment reset successfully")
            logger.info("About to get state from validation environment")
            val_actual_state = val_env._get_state()
            val_actual_state_dim = len(val_actual_state)
            logger.info(f"Got actual state from validation environment: dim={val_actual_state_dim}")
            # The observation_space and state_space are now correctly set within AsymmetricTradingEnv __init__
            val_actual_state_dim = val_env.observation_space.shape[0]
            logger.info(f"Val_env actual_state_dim after init: {val_actual_state_dim}")
            
            # Get environment dimensions from actual state sizes
            # Ensure state_dim for the agent is the observation_space dimension
            state_dim = train_env.observation_space.shape[0] 
            action_dim = train_env.action_space.shape[0]
            
            logger.info(f"Environment created: state_dim={state_dim}, action_dim={action_dim}")
            
        except Exception as e:
            logger.error(f"Environment creation failed: {e}")
            logger.exception(f"Environment creation failed with full traceback: {e}")
            sys.exit(1)
        
        # Create training configuration with best parameters
        training_config = TrainingConfig(
            total_timesteps=best_params.get('total_timesteps', settings.sac.total_timesteps),
            eval_freq=best_params.get('eval_freq', settings.sac.eval_freq),
            save_freq=best_params.get('save_freq', settings.sac.save_freq),
            log_interval=best_params.get('log_interval', settings.sac.log_interval)
        )
        
        # Create SAC agent with best parameters
        try:
            logger.info(f"Creating SAC agent with state_dim={state_dim}, action_dim={action_dim}")
            # Separate agent initialization parameters from config parameters
            agent_config = {
                'learning_rate': best_params.get('learning_rate', settings.sac.learning_rate),
                'gamma': best_params.get('gamma', settings.sac.gamma),
                'tau': best_params.get('soft_update_tau', settings.sac.tau),
                'alpha': best_params.get('alpha', settings.sac.alpha),
                'batch_size': best_params.get('batch_size', settings.sac.batch_size),
                'buffer_size': best_params.get('buffer_size', settings.sac.buffer_size),
                'net_dims': best_params.get('net_dims', settings.sac.net_dims),
                'target_step': best_params.get('target_step', settings.sac.target_step),
                'repeat_times': best_params.get('repeat_times', settings.sac.repeat_times),
                'reward_scale': best_params.get('reward_scale', settings.sac.reward_scale),
                'if_per': settings.sac.if_per,
                'if_off_policy': settings.sac.if_off_policy,
                'checkpoint_dir': settings.sac.checkpoint_dir
            }
            
            agent = SACAgent(
                state_dim=state_dim,
                action_dim=action_dim,
                config=agent_config,
                asymmetric_config=asymmetric_config
            )
            
            logger.info("SAC agent created successfully")
            logger.info(f"Agent parameters: {agent_config}")
            
        except Exception as e:
            logger.error(f"SAC agent creation failed: {e}")
            logger.error(f"Agent config: {agent_config}")
            logger.error(f"State dim: {state_dim}, Action dim: {action_dim}")
            logger.exception(f"Full traceback for SAC agent creation error:")
            sys.exit(1)
        
        # Load checkpoint if resuming
        if resume:
            try:
                checkpoint_path = Path(settings.sac.checkpoint_dir) / "latest_checkpoint.pth"
                if checkpoint_path.exists():
                    agent.load_checkpoint(str(checkpoint_path))
                    logger.info(f"Resumed training from checkpoint: {checkpoint_path}")
                else:
                    logger.warning("No checkpoint found. Starting fresh training.")
            except Exception as e:
                logger.warning(f"Failed to load checkpoint: {e}. Starting fresh training.")
        
        # Train the agent
        try:
            logger.info("Starting training...")
            results = agent.train(
                env=train_env,
                total_timesteps=training_config.total_timesteps,
                eval_env=val_env,
                eval_freq=training_config.eval_freq,
                save_freq=training_config.save_freq,
                model_dir=training_config.checkpoint_dir
            )
            
            if results['success']:
                logger.success(f"Training completed successfully. Final reward: {results.get('final_reward', 'N/A')}")
                logger.info(f"Model saved to: {results.get('model_path', 'N/A')}")
                
                # Save training results
                results_file = Path(settings.sac.checkpoint_dir) / "training_results.json"
                with open(results_file, 'w') as f:
                    json.dump(results, f, indent=2, default=str)
                logger.info(f"Training results saved to: {results_file}")
                
            else:
                raise Exception(f"Training failed: {results.get('error', 'unknown error')}")
                
        except Exception as e:
            logger.error(f"Training failed: {e}")
            sys.exit(1)
        
    except Exception as e:
        logger.exception(f"Training process failed: {e}")
        sys.exit(1)


@cli.command()
@click.option('--model', type=click.Path(exists=True), help='Trained model path')
@click.option('--start-date', type=click.DateTime(formats=['%Y-%m-%d']), help='Backtest start date')
@click.option('--end-date', type=click.DateTime(formats=['%Y-%m-%d']), help='Backtest end date')
@click.option('--output-dir', type=click.Path(), help='Results output directory')
@click.pass_context
def backtest(ctx: click.Context, model: Optional[str], start_date: Optional[str], 
             end_date: Optional[str], output_dir: Optional[str]):
    """Historical performance evaluation"""
    from backtesting.engine import BacktestEngine
    
    settings = ctx.obj['settings']
    logger.info("Starting backtesting")
    
    try:
        engine = BacktestEngine(settings)
        
        results = engine.run_backtest(
            model_path=model,
            start_date=start_date or settings.data.validation_start_date,
            end_date=end_date or settings.data.validation_end_date,
            output_dir=output_dir or settings.backtesting.results_dir
        )
        
        logger.success(f"Backtesting completed. Results: {results}")
        
    except Exception as e:
        logger.error(f"Backtesting failed: {e}")
        sys.exit(1)


@cli.command()
@click.option('--model', type=click.Path(exists=True), help='Trained model path')
@click.option('--duration', type=int, help='Trading duration in days')
@click.option('--dry-run', is_flag=True, help='Simulation mode without real trades')
@click.pass_context
def papertrade(ctx: click.Context, model: Optional[str], duration: Optional[int], dry_run: bool):
    """Live paper trading simulation"""
    from trading.executor import PaperTradingExecutor
    
    settings = ctx.obj['settings']
    logger.info("Starting paper trading")
    
    try:
        executor = PaperTradingExecutor(settings)
        
        results = executor.start_trading(
            model_path=model,
            duration_days=duration or 30,
            dry_run=dry_run
        )
        
        logger.success(f"Paper trading completed. Results: {results}")
        
    except Exception as e:
        logger.error(f"Paper trading failed: {e}")
        sys.exit(1)


if __name__ == '__main__':
    cli()