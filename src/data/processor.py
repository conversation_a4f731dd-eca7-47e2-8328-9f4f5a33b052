"""Data processor module for technical indicators and feature engineering.

This module provides:
- Technical indicator calculation using pandas_ta
- VIX data integration and regime detection
- Feature engineering for RL training
- Data preprocessing and normalization
- Market regime classification
"""

import pandas as pd
import pandas_ta as ta
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime

from config import settings, VIX_REGIMES, TECH_INDICATORS
from utils import get_logger, log_performance, log_data_quality
from .validator import DataValidator


class DataProcessor:
    """Advanced data processor with technical indicators and VIX integration."""
    
    def __init__(self, fetcher, settings_obj, tech_indicator_list: List[str], vix_features: List[str], include_vix: bool):
        """Initialize the data processor."""
        self.logger = get_logger(__name__)
        self.fetcher = fetcher
        self.settings = settings_obj  # Store the passed settings object
        self.validator = DataValidator()
        self.indicator_config = self.settings.indicators # Use stored settings
        self.tech_indicator_list = tech_indicator_list
        self.vix_features = vix_features
        self.include_vix = include_vix

        self.logger.info("DataProcessor initialized with pandas_ta integration")
    
    @log_performance
    def process_stock_data(
        self,
        data: pd.DataFrame,
        add_indicators: bool = True,
        normalize: bool = False
    ) -> pd.DataFrame:
        """Process stock data with technical indicators.
        
        Args:
            data: Raw stock data with OHLCV columns
            add_indicators: Whether to add technical indicators
            normalize: Whether to normalize the data
            
        Returns:
            Processed DataFrame with technical indicators
        """
        if data.empty:
            self.logger.warning("Empty data provided for processing")
            return data
        
        self.logger.info(f"Processing stock data: {len(data)} records")
        
        # Create a copy to avoid modifying original data
        processed_data = data.copy()
        
        # Ensure proper column names and data types
        # Rename 'symbol' to 'tic' EARLY if it exists, to standardize the ticker column name.
        # This should happen before standardization and sorting.
        if 'symbol' in processed_data.columns and 'tic' not in processed_data.columns:
            self.logger.info("Early rename of 'symbol' to 'tic' in process_stock_data.")
            processed_data = processed_data.rename(columns={'symbol': 'tic'})
        elif 'symbol' in processed_data.columns and 'tic' in processed_data.columns:
            self.logger.warning("'symbol' and 'tic' columns both exist. Prioritizing 'tic' and dropping 'symbol'.")
            processed_data = processed_data.drop(columns=['symbol'])

        processed_data = self._standardize_columns(processed_data)
        
        # Sort data by tic and date
        if 'tic' in processed_data.columns:
            processed_data = processed_data.sort_values(by=['tic', 'date'], ascending=[True, True])
        else:
            self.logger.warning("'tic' column not found for sorting. Data might not be in expected order. This could be an issue if 'symbol' was also not found or not renamed.")

        # Add technical indicators
        if add_indicators:
            processed_data = self._add_technical_indicators(processed_data)
        
        # Add derived features
        processed_data = self._add_derived_features(processed_data)
        
        # Normalize data if requested
        if normalize:
            processed_data = self._normalize_data(processed_data)
        
        # Merge with VIX data if include_vix is True
        if self.include_vix:
            # Assuming VIX data is fetched and processed separately, then passed or loaded here
            # For now, let's assume a method to get processed VIX data exists or is called before this
            # This part needs to be connected to actual VIX data fetching and processing logic
            try:
                # Attempt to load pre-processed VIX data if available (e.g., from a file or another source)
                # This is a placeholder for actual VIX data loading/retrieval
                vix_df = self.fetcher.fetch_vix_data(start_date=data['date'].min(), end_date=data['date'].max())
                if vix_df is not None and not vix_df.empty:
                    processed_vix_df = self.process_vix_data(vix_df, add_indicators=True)
                    
                    # Ensure the main data (processed_data) doesn't have VIX columns before merge
                    # to prevent _x, _y suffixes from pandas.merge if names overlap.
                    # The VIX columns should solely come from processed_vix_df.
                    cols_to_drop_from_left = [
                        c for c in processed_data.columns 
                        if c.startswith('VIX_') and c != 'date' # 'date' is the merge key
                    ]
                    if cols_to_drop_from_left:
                        self.logger.info(
                            f"Removing pre-existing VIX-related columns from stock data before merge: {cols_to_drop_from_left}"
                        )
                        processed_data = processed_data.drop(columns=cols_to_drop_from_left)
                        
                    processed_data = self.merge_with_vix(processed_data, processed_vix_df)
                    self.logger.info("Successfully merged VIX data during stock data processing.")
                else:
                    self.logger.warning("VIX data not available or empty, skipping merge in process_stock_data.")
            except Exception as e:
                self.logger.error(f"Error during VIX data merging in process_stock_data: {e}", exc_info=True)

        # Remove any rows with NaN values in critical columns
        processed_data = self._clean_processed_data(processed_data)

        # Ensure no duplicate columns before returning
        if processed_data.columns.has_duplicates:
            duplicate_cols = processed_data.columns[processed_data.columns.duplicated()].tolist()
            self.logger.warning(
                f"Processed data has duplicate columns: {duplicate_cols}. Keeping first occurrence of each."
            )
            processed_data = processed_data.loc[:, ~processed_data.columns.duplicated(keep='first')]
            self.logger.info(
                f"Removed duplicate columns. New column count: {len(processed_data.columns)}"
            )
        
        self.logger.success(
            f"Successfully processed data: {len(processed_data)} records, "
            f"{len(processed_data.columns)} features"
        )
        
        return processed_data
    
    @log_performance
    def process_vix_data(
        self,
        vix_data: pd.DataFrame,
        add_indicators: bool = True
    ) -> pd.DataFrame:
        """Process VIX data with volatility indicators.
        
        Args:
            vix_data: Raw VIX data
            add_indicators: Whether to add VIX-specific indicators
            
        Returns:
            Processed VIX DataFrame
        """
        if vix_data.empty:
            self.logger.warning("Empty VIX data provided")
            return vix_data
        
        import os # For PID
        pid = os.getpid()
        self.logger.info(f"[PID:{pid}] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: {len(self.tech_indicator_list)}): {self.tech_indicator_list}")
        self.logger.info(f"Processing VIX data: {len(vix_data)} records")
        
        processed_vix = vix_data.copy()
        processed_vix = self._standardize_columns(processed_vix)
        
        if add_indicators:
            processed_vix = self._add_vix_indicators(processed_vix)
        
        # Add VIX regime classification
        processed_vix = self._add_vix_regimes(processed_vix)
        
        self.logger.success(f"Successfully processed VIX data: {len(processed_vix)} records")
        
        return processed_vix
    
    def _standardize_columns(self, data: pd.DataFrame) -> pd.DataFrame:
        """Standardize column names and data types.
        
        Args:
            data: DataFrame to standardize
            
        Returns:
            Standardized DataFrame
        """
        # Ensure date column is datetime
        if 'date' in data.columns:
            try:
                # Convert to datetime, normalize to midnight, and remove timezone
                data['date'] = pd.to_datetime(data['date']).dt.normalize().dt.tz_localize(None)
            except Exception as e:
                self.logger.warning(f"Error standardizing date column (attempt 1): {e}. Trying with specific format.")
                try:
                    # Try common formats if direct conversion fails
                    data['date'] = pd.to_datetime(data['date'], errors='coerce').dt.normalize().dt.tz_localize(None)
                    if data['date'].isnull().any():
                        self.logger.warning("NaT values produced after second attempt at date conversion.")
                except Exception as e2:
                    self.logger.error(f"Failed to standardize date column after multiple attempts: {e2}")
        
        # Ensure numeric columns are float
        numeric_columns = ['open', 'high', 'low', 'close', 'volume']
        for col in numeric_columns:
            if col in data.columns:
                data[col] = pd.to_numeric(data[col], errors='coerce')
        
        return data
    
    def _add_technical_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """Add technical indicators using pandas_ta.
        
        Args:
            data: DataFrame with OHLCV data
            
        Returns:
            DataFrame with technical indicators
        """
        import os # For PID
        pid = os.getpid()
        self.logger.info(f"[PID:{pid}] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: {len(self.tech_indicator_list)}): {self.tech_indicator_list}")

        # Standardize to 'tic' column for ticker identification
        if 'symbol' in data.columns and 'tic' not in data.columns:
            self.logger.info("Renaming 'symbol' to 'tic' in _add_technical_indicators as 'tic' is preferred.")
            data = data.rename(columns={'symbol': 'tic'})
        elif 'tic' not in data.columns and 'symbol' not in data.columns:
            # If neither 'tic' nor 'symbol' is present, process as single block but log warning.
            self.logger.warning("Neither 'tic' nor 'symbol' column found. Processing data as a single block.")
            # Process as single symbol - try to infer symbol from data or use a more descriptive name
            inferred_tic = 'SINGLE_BLOCK_DATA'
            if len(data) > 0:
                inferred_tic = f'DATA_BLOCK_{len(data)}_ROWS'
            self.logger.info(f"Processing data as: {inferred_tic}")
            return self._calculate_indicators_for_symbol(data, inferred_tic)
        elif 'symbol' in data.columns and 'tic' in data.columns:
            self.logger.warning("Both 'symbol' and 'tic' columns found. Prioritizing 'tic' and dropping 'symbol'.")
            data = data.drop(columns=['symbol'])

        # At this point, data should have a 'tic' column if it's multi-stock data.
        # Or it's being treated as a single block if no 'tic' was found.

        if 'tic' in data.columns:
            # Process each tic separately
            processed_groups = []
            # Filter out NaN or invalid tics
            valid_tics = [
                s for s in data['tic'].unique() 
                if pd.notna(s) and str(s).strip() and str(s).lower() != 'nan'
            ]
            if not valid_tics:
                self.logger.warning("No valid tics found for technical indicator calculation after standardization.")
                return data # Or an empty DataFrame with original columns

            for tic_value in valid_tics:
                tic_data = data[data['tic'] == tic_value].copy()
                tic_data = self._calculate_indicators_for_symbol(tic_data, str(tic_value))
                processed_groups.append(tic_data)
            
            if not processed_groups: # Handle case where no valid tics are found after processing individuals
                self.logger.warning("No data after processing individual tics.")
                return data # Or an empty DataFrame with original columns
            
            concatenated_data = pd.concat(processed_groups, ignore_index=True)
            # 'tic' column should already be correctly named here.
            return concatenated_data
        else:
            # This case should ideally be covered by the 'neither tic nor symbol' block above.
            # If reached, it implies data was single block and already processed by _calculate_indicators_for_symbol.
            self.logger.info("Data processed as a single block (no 'tic' column for grouping).")
            return data # Data should have been processed by the earlier call if no 'tic'/'symbol'
    
    def _calculate_indicators_for_symbol(
        self,
        data: pd.DataFrame,
        symbol: str
    ) -> pd.DataFrame:
        """Calculate technical indicators for a single symbol.
        
        Args:
            data: Symbol data
            symbol: Stock symbol
            
        Returns:
            DataFrame with indicators
        """
        if len(data) < self.indicator_config.min_data_points_for_indicators: # Use a configurable min length
            self.logger.warning(f"Insufficient data for indicators: {symbol} ({len(data)} records) - requires at least {self.indicator_config.min_data_points_for_indicators}")
            # Return data with expected indicator columns filled with NaN if they don't exist
            # This ensures consistent columns for pd.concat later.
            # Simplified: for now, just return data. A more robust solution would add NaN columns.
            return data
        
        try:
            # Ensure data is sorted by date if 'date' column exists
            if 'date' in data.columns:
                data = data.sort_values('date').reset_index(drop=True)
                
                # Check for and handle duplicate dates
                if data['date'].duplicated().any():
                    self.logger.warning(f"Duplicate dates found for {symbol}, removing duplicates")
                    data = data.drop_duplicates(subset=['date'], keep='last').reset_index(drop=True)

            # Set the index for pandas_ta
            data_with_index = data.set_index('date') if 'date' in data.columns else data.copy() # Use .copy() if no 'date' index

            # Ensure 'symbol' column is not present before calling pandas_ta functions
            if 'symbol' in data_with_index.columns:
                self.logger.info(f"Removing 'symbol' column from data_with_index for {symbol} before pandas_ta calls.")
                data_with_index = data_with_index.drop(columns=['symbol'])
            
            # Trend Indicators
            for period in self.indicator_config.sma_periods:
                sma_values = ta.sma(data_with_index['close'], length=period)
                if sma_values is not None:
                    data[f'sma_{period}'] = sma_values.values
            
            for period in self.indicator_config.ema_periods:
                ema_values = ta.ema(data_with_index['close'], length=period)
                if ema_values is not None:
                    data[f'ema_{period}'] = ema_values.values
            
            macd_data = ta.macd(
                data_with_index['close'],
                fast=self.indicator_config.macd_fast,
                slow=self.indicator_config.macd_slow,
                signal=self.indicator_config.macd_signal
            )
            if macd_data is not None and not macd_data.empty:
                for col in macd_data.columns:
                    data[col.lower()] = macd_data[col].values

            # Momentum Indicators
            for period in self.indicator_config.rsi_periods:
                rsi_values = ta.rsi(data_with_index['close'], length=period)
                if rsi_values is not None:
                    data[f'rsi_{period}'] = rsi_values.values
            
            for period in self.indicator_config.cci_periods:
                cci_values = ta.cci(data_with_index['high'], data_with_index['low'], data_with_index['close'], length=period)
                if cci_values is not None:
                    data[f'cci_{period}'] = cci_values.values

            adx_data = ta.adx(
                data_with_index['high'], 
                data_with_index['low'], 
                data_with_index['close'], 
                length=self.indicator_config.adx_length
            )
            if adx_data is not None and not adx_data.empty:
                for col in adx_data.columns:
                    # ADX typically returns ADX_length, DMP_length, DMN_length
                    if col.startswith(('ADX_', 'DMP_', 'DMN_')):
                         data[col.lower()] = adx_data[col].values

            # Volatility Indicators
            bband_data = ta.bbands(
                data_with_index['close'], 
                length=self.indicator_config.bbands_length, 
                std=self.indicator_config.bbands_std
            )
            if bband_data is not None and not bband_data.empty:
                for col in bband_data.columns:
                    data[col.lower()] = bband_data[col].values

            # Volume Indicators (Example: On-Balance Volume)
            if 'volume' in data_with_index.columns:
                obv_values = ta.obv(data_with_index['close'], data_with_index['volume'])
                if obv_values is not None:
                    data['obv'] = obv_values.values

            # Ensure 'date' column is present if it was used as index and then reset
            if 'date' not in data.columns and isinstance(data_with_index.index, pd.DatetimeIndex):
                 data['date'] = data_with_index.index.values

        except Exception as e:
            self.logger.error(f"Error calculating indicators for {symbol}: {e}")
            self.logger.error(f"Data shape: {data.shape}, columns: {list(data.columns)}")
            if 'date' in data.columns:
                self.logger.error(f"Date range: {data['date'].min()} to {data['date'].max()}")
                self.logger.error(f"Duplicate dates: {data['date'].duplicated().sum()}")
            self.logger.exception(f"Full traceback for {symbol} indicator calculation error:")
            
            # Return original data to prevent downstream errors
            return data
            
        return data
    
    def _add_vix_indicators(self, vix_data: pd.DataFrame) -> pd.DataFrame:
        """Add VIX-specific indicators.
        
        Args:
            vix_data: VIX data
            
        Returns:
            DataFrame with VIX indicators
        """
        if len(vix_data) < 20:
            return vix_data
        
        try:
            # Set index for pandas_ta
            vix_indexed = vix_data.set_index('date') if 'date' in vix_data.columns else vix_data
            
            # VIX moving averages
            for period in self.indicator_config.vix_ma_periods:
                sma_series = ta.sma(vix_indexed['close'], length=period)
                if sma_series is not None:
                    vix_data[f'vix_ma_{period}'] = sma_series.values
            
            # VIX percentiles
            window = self.indicator_config.vix_percentile_window
            percentile_series = vix_indexed['close'].rolling(window=window, min_periods=1).rank(pct=True) * 100 # Added min_periods=1
            if percentile_series is not None:
                vix_data[f'vix_percentile_{window}'] = percentile_series.values
            
            # VIX term structure
            if len(vix_indexed) > 1: # Check on vix_indexed as pct_change is called on it
                change_series = vix_indexed['close'].pct_change()
                if change_series is not None:
                    vix_data['vix_change'] = change_series.values
            
            if len(vix_indexed) > 5: # Check for 5-day change
                change_5d_series = vix_indexed['close'].pct_change(periods=5)
                if change_5d_series is not None:
                    vix_data['vix_change_5d'] = change_5d_series.values
            
            self.logger.debug("Added VIX indicators")
            
        except Exception as e:
            self.logger.error(f"Failed to calculate VIX indicators: {e}")
        
        return vix_data
    
    def _add_vix_regimes(self, vix_data: pd.DataFrame) -> pd.DataFrame:
        """Add VIX regime classification.
        
        Args:
            vix_data: VIX data
            
        Returns:
            DataFrame with regime classification
        """
        if 'close' not in vix_data.columns:
            return vix_data
        
        vix_levels = vix_data['close']
        
        # Classify VIX regimes based on levels
        conditions = [
            vix_levels < VIX_REGIMES['low_volatility']['max'],
            (vix_levels >= VIX_REGIMES['low_volatility']['max']) &
            (vix_levels < VIX_REGIMES['moderate_volatility']['max']),
            (vix_levels >= VIX_REGIMES['moderate_volatility']['max']) &
            (vix_levels < VIX_REGIMES['high_volatility']['max']),
            vix_levels >= VIX_REGIMES['high_volatility']['max']
        ]
        
        # Corrected choices based on VIX_REGIMES keys in constants.py
        choices = ['low_volatility', 'moderate_volatility', 'high_volatility', 'extreme_volatility']
        
        vix_data['vix_regime'] = np.select(conditions, choices, default='medium')
        
        # Add regime as numeric for ML - map actual regime values
        regime_mapping = {
            'low_volatility': 0.0, 
            'moderate_volatility': 1.0, 
            'high_volatility': 2.0, 
            'extreme_volatility': 3.0,
            'medium': 1.0  # fallback default
        }
        vix_data['vix_regime_numeric'] = vix_data['vix_regime'].map(regime_mapping).fillna(1.0).astype(np.float32)
        
        self.logger.debug("Added VIX regime classification")
        
        return vix_data
    
    def _add_derived_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Add derived features for enhanced ML performance.
        
        Args:
            data: DataFrame with basic indicators
            
        Returns:
            DataFrame with derived features
        """
        try:
            # Price-based features
            if all(col in data.columns for col in ['high', 'low', 'close']):
                data['price_range'] = (data['high'] - data['low']) / data['close']
                data['price_position'] = (data['close'] - data['low']) / (data['high'] - data['low'])
            
            # Returns
            if 'close' in data.columns:
                data['returns_1d'] = data['close'].pct_change(fill_method=None)
                data['returns_5d'] = data['close'].pct_change(periods=5, fill_method=None)
                data['returns_20d'] = data['close'].pct_change(periods=20, fill_method=None)
            
            # Volume features
            if 'volume' in data.columns:
                data['volume_ma_20'] = data['volume'].rolling(window=20).mean()
                data['volume_ratio'] = data['volume'] / data['volume_ma_20']
            
            # Volatility features
            if 'returns_1d' in data.columns:
                data['volatility_20d'] = data['returns_1d'].rolling(window=20).std()
            
            # Add turbulence calculation
            data = self._add_turbulence_index(data)
            
            self.logger.debug("Added derived features")
            
        except Exception as e:
            self.logger.error(f"Failed to add derived features: {e}")
        
        return data
    
    def _normalize_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Normalize numerical features.
        
        Args:
            data: DataFrame to normalize
            
        Returns:
            Normalized DataFrame
        """
        # Identify columns to normalize (exclude categorical and date columns)
        exclude_columns = ['date', 'symbol', 'tic', 'vix_regime']
        numeric_columns = [
            col for col in data.select_dtypes(include=[np.number]).columns
            if col not in exclude_columns
        ]
        
        if 'symbol' in data.columns:
            # Normalize each symbol separately
            normalized_groups = []
            for symbol in data['symbol'].unique():
                symbol_data = data[data['symbol'] == symbol].copy()
                
                for col in numeric_columns:
                    if col in symbol_data.columns:
                        # Use rolling normalization to avoid look-ahead bias
                        rolling_mean = symbol_data[col].expanding().mean()
                        rolling_std = symbol_data[col].expanding().std()
                        symbol_data[f'{col}_normalized'] = (
                            (symbol_data[col] - rolling_mean) / (rolling_std + 1e-8)
                        )
                
                normalized_groups.append(symbol_data)
            
            return pd.concat(normalized_groups, ignore_index=True)
        else:
            # Normalize single symbol
            for col in numeric_columns:
                if col in data.columns:
                    rolling_mean = data[col].expanding().mean()
                    rolling_std = data[col].expanding().std()
                    data[f'{col}_normalized'] = (
                        (data[col] - rolling_mean) / (rolling_std + 1e-8)
                    )
        
        return data
    
    def _clean_processed_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Clean processed data by handling NaN values.
        
        Args:
            data: Processed DataFrame
            
        Returns:
            Cleaned DataFrame
        """
        original_length = len(data)
        
        # Forward fill NaN values (limited)
        numeric_columns = data.select_dtypes(include=[np.number]).columns
        data[numeric_columns] = data[numeric_columns].ffill(limit=5)
        
        # Drop rows with NaN values in critical columns (should be handled by fetcher, but double-check)
        critical_columns = ['open', 'high', 'low', 'close', 'volume']
        available_critical = [col for col in critical_columns if col in data.columns]
        data = data.dropna(subset=available_critical)
        
        # Fill remaining NaNs in technical indicators and VIX features with 0
        # This is crucial for RL environments that cannot handle NaNs.
        cols_to_fill_zero = self.tech_indicator_list + [col for col in data.columns if 'vix_' in col and col != 'vix_regime']

        # --- DETAILED LOGGING FOR VIX NAN FILLING --- 
        self.logger.info("PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.")
        problematic_vix_cols_log = ['vix_ma_5_y', 'vix_ma_20_y', 'vix_change_5d_y']
        # Log state for AAPL before the loop, if AAPL and problem columns exist
        if 'AAPL' in data['tic'].unique() and any(pvc in data.columns for pvc in problematic_vix_cols_log):
            aapl_data_subset = data[data['tic'] == 'AAPL']
            self.logger.info("PROCESSOR_CLEAN: AAPL data BEFORE main fillna(0) loop for problem VIX cols:")
            for pvc in problematic_vix_cols_log:
                if pvc in aapl_data_subset.columns:
                    self.logger.info(f"  AAPL - {pvc}: dtype={aapl_data_subset[pvc].dtype}, NaNs={aapl_data_subset[pvc].isnull().sum()}")
        # --- END DETAILED LOGGING PREAMBLE ---
        
        for col in cols_to_fill_zero:
            if col in data.columns:
                is_problem_vix_col = col in problematic_vix_cols_log
                original_nan_count = -1

                if is_problem_vix_col:
                    original_nan_count = data[col].isnull().sum()
                    self.logger.info(f"PROCESSOR_CLEAN: VIX col '{col}': dtype={data[col].dtype}, NaNs BEFORE fill: {original_nan_count}")

                if pd.api.types.is_numeric_dtype(data[col]):
                    data[col] = data[col].fillna(0)
                    if is_problem_vix_col:
                        filled_nan_count = data[col].isnull().sum()
                        self.logger.info(f"PROCESSOR_CLEAN: VIX col '{col}': NaNs AFTER fill: {filled_nan_count}")
                        if original_nan_count > 0 and filled_nan_count == original_nan_count:
                            self.logger.warning(f"PROCESSOR_CLEAN: VIX col '{col}' still has {filled_nan_count} NaNs after fillna(0)! Dtype was {data[data[col].dtype]}.") # Corrected dtype logging
                else:
                    if data[col].isnull().any():
                        self.logger.warning(f"PROCESSOR_CLEAN: Column '{col}' is not numeric (dtype: {data[col].dtype}) and has NaNs. Not filling with 0.")
        
        # --- DETAILED LOGGING FOR VIX NAN FILLING POST --- 
        if 'AAPL' in data['tic'].unique() and any(pvc in data.columns for pvc in problematic_vix_cols_log):
            aapl_data_subset_post = data[data['tic'] == 'AAPL']
            self.logger.info("PROCESSOR_CLEAN: AAPL data AFTER main fillna(0) loop for problem VIX cols:")
            for pvc in problematic_vix_cols_log:
                if pvc in aapl_data_subset_post.columns:
                    self.logger.info(f"  AAPL - {pvc}: dtype={aapl_data_subset_post[pvc].dtype}, NaNs={aapl_data_subset_post[pvc].isnull().sum()}")
        # --- END DETAILED LOGGING POST --- 

        # Define potential_feature_columns before use
        potential_feature_columns = [col for col in data.columns if col not in available_critical and col not in ['date', 'tic']]

        # Final check for any remaining NaNs after all cleaning
        # TODO: Consider re-adding a configuration for this check if needed.
        # The check for self.settings.data.cleaning.final_nan_check was removed due to AttributeError.
        
        # Log if any rows were removed based on critical columns
        rows_removed_critical = original_length - len(data)
        if rows_removed_critical > 0:
            self.logger.info(f"Cleaned processed data: removed {rows_removed_critical} rows with NaN values in critical columns")

        # Final check: drop any rows that still have NaNs in any feature or critical column
        # This ensures no NaNs are passed to downstream processes after ffill and specific fillna(0)
        columns_to_check_for_nan = available_critical + potential_feature_columns
        # Ensure only existing columns are used for dropping NaNs
        columns_to_check_for_nan = [col for col in columns_to_check_for_nan if col in data.columns]
        
        if columns_to_check_for_nan:
            data_before_final_nan_drop = len(data)
            data = data.dropna(subset=columns_to_check_for_nan)
            rows_removed_final_check = data_before_final_nan_drop - len(data)
            if rows_removed_final_check > 0:
                self.logger.info(f"Cleaned processed data: removed an additional {rows_removed_final_check} rows during final NaN check in feature/critical columns.")
        
        return data.reset_index(drop=True)
    
    def merge_with_vix(
        self,
        stock_data: pd.DataFrame,
        vix_data: pd.DataFrame
    ) -> pd.DataFrame:
        """Merge stock data with VIX data for regime-aware features.
        
        Args:
            stock_data: Processed stock data
            vix_data: Processed VIX data
            
        Returns:
            Merged DataFrame with VIX features
        """
        if vix_data.empty:
            self.logger.warning("Empty VIX data, skipping merge")
            return stock_data
        
        import os # For PID
        pid = os.getpid()

        # Prepare VIX data for merging
        vix_merge = vix_data[['date'] + [col for col in vix_data.columns if col.startswith('vix')]].copy()

        # Ensure the left DataFrame (stock_data) does not have VIX columns before merge
        # to prevent _x, _y suffixes if names overlap.
        cols_to_drop_from_left = [
            c for c in stock_data.columns 
            if c.startswith('vix_') and c != 'date' # 'date' is the merge key
        ]
        if cols_to_drop_from_left:
            self.logger.info(
                f"[PID:{pid}] Removing pre-existing VIX-related columns from stock_data in merge_with_vix: {cols_to_drop_from_left}"
            )
            stock_data = stock_data.drop(columns=cols_to_drop_from_left)
        
        # Merge on date
        merged_data = pd.merge(
            stock_data,
            vix_merge,
            on='date',
            how='left'
        )
        
        # Forward fill VIX data for missing dates
        vix_columns_in_merged_data = [col for col in merged_data.columns if col.startswith('vix')]
        
        # Identify VIX feature names that were actually added to vix_merge and are now in merged_data
        # These are the columns (excluding 'date') from vix_merge that start with 'vix'
        added_vix_feature_names = [col for col in vix_merge.columns if col.startswith('vix') and col != 'date']
        
        # Extend tech_indicator_list only with newly added VIX features not already present
        newly_added_vix_for_list = [f_name for f_name in added_vix_feature_names if f_name not in self.tech_indicator_list]
        if newly_added_vix_for_list:
            self.tech_indicator_list.extend(newly_added_vix_for_list)
            self.logger.info(f"[PID:{pid}] DataProcessor.merge_with_vix: Extended tech_indicator_list with VIX features (len: {len(self.tech_indicator_list)}): {newly_added_vix_for_list}")
            self.logger.debug(f"[PID:{pid}] DataProcessor.merge_with_vix: Full tech_indicator_list now: {self.tech_indicator_list}")
        
        # Ensure VIX columns (except categorical vix_regime and its suffixed versions) are numeric before ffill
        for col in vix_columns_in_merged_data:
            # Check if the column is one of the vix_regime string columns
            is_vix_regime_str_col = (col == 'vix_regime' or col == 'vix_regime_x' or col == 'vix_regime_y')
            
            if is_vix_regime_str_col:
                self.logger.debug(f"Skipping numeric conversion for categorical vix regime column: {col}")
                continue  # Skip to next column if it's a vix_regime string column
                
            if merged_data[col].dtype == 'object':
                self.logger.debug(f"Attempting numeric conversion for VIX object column: {col}")
                merged_data[col] = pd.to_numeric(merged_data[col], errors='coerce')
        
        merged_data[vix_columns_in_merged_data] = merged_data[vix_columns_in_merged_data].ffill()
        # After ffill, infer best possible dtypes. copy=False modifies in place if possible.
        merged_data[vix_columns_in_merged_data] = merged_data[vix_columns_in_merged_data].infer_objects(copy=False)
        
        self.logger.info(f"Merged stock data with VIX: {len(merged_data)} records")
        
        return merged_data
    
    def prepare_for_training(
        self,
        data: pd.DataFrame,
        feature_columns: Optional[List[str]] = None
    ) -> Tuple[pd.DataFrame, List[str]]:
        """Prepare data for RL training.
        
        Args:
            data: Processed data
            feature_columns: Specific columns to use as features
            
        Returns:
            Tuple of (prepared_data, feature_column_names)
        """
        if feature_columns is None:
            # Auto-select feature columns
            exclude_columns = ['date', 'symbol', 'tic']
            feature_columns = [
                col for col in data.columns
                if col not in exclude_columns and data[col].dtype in ['float64', 'int64']
            ]
        
        # Ensure all feature columns exist
        available_features = [col for col in feature_columns if col in data.columns]
        
        if len(available_features) != len(feature_columns):
            missing_features = set(feature_columns) - set(available_features)
            self.logger.warning(f"Missing feature columns: {missing_features}")
        
        # Prepare final dataset
        training_data = data.copy()
        
        # Sort by symbol and date for proper sequence
        if 'tic' in training_data.columns and 'date' in training_data.columns:
            training_data = training_data.sort_values(['tic', 'date']).reset_index(drop=True)
        elif 'symbol' in training_data.columns and 'date' in training_data.columns:
            training_data = training_data.sort_values(['symbol', 'date']).reset_index(drop=True)
        
        self.logger.info(
            f"Prepared data for training: {len(training_data)} records, "
            f"{len(available_features)} features"
        )
        
        return training_data, available_features
    
    def get_feature_importance_data(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Get data for feature importance analysis.
        
        Args:
            data: Processed data
            
        Returns:
            Dictionary with feature statistics
        """
        numeric_columns = data.select_dtypes(include=[np.number]).columns
        
        feature_stats = {}
        for col in numeric_columns:
            if col in data.columns:
                feature_stats[col] = {
                    'mean': float(data[col].mean()),
                    'std': float(data[col].std()),
                    'min': float(data[col].min()),
                    'max': float(data[col].max()),
                    'null_count': int(data[col].isna().sum()),
                    'null_percentage': float(data[col].isna().mean() * 100)
                }
        
        return {
            'total_features': len(numeric_columns),
            'total_records': len(data),
            'feature_statistics': feature_stats
        }
    
    def _add_turbulence_index(self, data: pd.DataFrame) -> pd.DataFrame:
        """Add turbulence index using Mahalanobis distance.
        
        The turbulence index measures statistical unusualness of asset returns
        based on their historical multivariate distribution.
        
        Formula: dt = (1/n) * (rt - μ)^T * Σ^(-1) * (rt - μ)
        where:
        - rt: current period returns vector
        - μ: historical mean returns vector
        - Σ: historical covariance matrix
        - n: number of assets
        
        Args:
            data: DataFrame with returns data
            
        Returns:
            DataFrame with turbulence column added
        """
        try:
            if 'tic' not in data.columns or 'returns_1d' not in data.columns:
                self.logger.warning("Missing required columns for turbulence calculation")
                data['turbulence'] = 0.0
                return data
            
            # Get unique dates and tickers
            dates = sorted(data['date'].unique())
            tickers = sorted(data['tic'].unique())
            
            if len(tickers) < 2:
                self.logger.warning("Need at least 2 tickers for turbulence calculation")
                data['turbulence'] = 0.0
                return data
            
            # Create returns matrix (dates x tickers)
            returns_matrix = data.pivot(index='date', columns='tic', values='returns_1d')
            returns_matrix = returns_matrix.fillna(0.0)  # Fill NaN with 0
            
            # Calculate turbulence for each date
            turbulence_values = []
            lookback_window = 252  # 1 year of trading days
            
            for i, date in enumerate(dates):
                if i < lookback_window:
                    # Not enough history, set turbulence to 0
                    turbulence_values.append(0.0)
                    continue
                
                # Get historical returns for covariance calculation
                hist_start_idx = max(0, i - lookback_window)
                hist_returns = returns_matrix.iloc[hist_start_idx:i]
                
                # Current period returns
                current_returns = returns_matrix.iloc[i]
                
                # Skip if insufficient data
                if len(hist_returns) < 20 or hist_returns.std().min() == 0:
                    turbulence_values.append(0.0)
                    continue
                
                try:
                    # Calculate historical mean and covariance
                    hist_mean = hist_returns.mean()
                    hist_cov = hist_returns.cov()
                    
                    # Add small regularization to diagonal for numerical stability
                    regularization = 1e-6
                    hist_cov += np.eye(len(hist_cov)) * regularization
                    
                    # Calculate Mahalanobis distance (turbulence)
                    diff = current_returns - hist_mean
                    
                    # Compute turbulence using matrix operations
                    turbulence = float(diff.T @ np.linalg.inv(hist_cov) @ diff) / len(tickers)
                    
                    # Ensure turbulence is non-negative and finite
                    if np.isfinite(turbulence) and turbulence >= 0:
                        turbulence_values.append(turbulence)
                    else:
                        turbulence_values.append(0.0)
                        
                except (np.linalg.LinAlgError, ValueError) as e:
                    self.logger.debug(f"Turbulence calculation failed for {date}: {e}")
                    turbulence_values.append(0.0)
            
            # Create turbulence DataFrame
            turbulence_df = pd.DataFrame({
                'date': dates,
                'turbulence': turbulence_values
            })
            
            # Merge turbulence back to original data
            data = data.merge(turbulence_df, on='date', how='left')
            data['turbulence'] = data['turbulence'].fillna(0.0)
            
            self.logger.info(f"Added turbulence index: mean={data['turbulence'].mean():.4f}, max={data['turbulence'].max():.4f}")
            
        except Exception as e:
            self.logger.error(f"Failed to calculate turbulence index: {e}")
            data['turbulence'] = 0.0
        
        return data