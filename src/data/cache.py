"""Data caching module for efficient data storage and retrieval.

This module provides:
- CSV-based caching with timestamp validation
- Smart cache invalidation based on data freshness
- Efficient data serialization and deserialization
- Cache management and cleanup utilities
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from pathlib import Path
import json
import hashlib
import os

from config import settings
from utils import get_logger, log_performance


class DataCache:
    """High-performance data cache with smart invalidation."""
    
    def __init__(self, cache_dir: Path):
        """Initialize the data cache.
        
        Args:
            cache_dir: Directory for storing cached data
        """
        self.cache_dir = Path(cache_dir)
        self.logger = get_logger(__name__)
        self.metadata_file = self.cache_dir / "cache_metadata.json"
        
        # Create cache directory
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # Load or create metadata
        self.metadata = self._load_metadata()
        
        self.logger.debug(f"DataCache initialized: {self.cache_dir}")
    
    def _load_metadata(self) -> Dict[str, Any]:
        """Load cache metadata from file.
        
        Returns:
            Dictionary with cache metadata
        """
        if self.metadata_file.exists():
            try:
                with open(self.metadata_file, 'r') as f:
                    return json.load(f)
            except Exception as e:
                self.logger.warning(f"Failed to load cache metadata: {e}")
        
        return {}
    
    def _save_metadata(self) -> None:
        """Save cache metadata to file."""
        try:
            with open(self.metadata_file, 'w') as f:
                json.dump(self.metadata, f, indent=2, default=str)
        except Exception as e:
            self.logger.error(f"Failed to save cache metadata: {e}")
    
    def _get_cache_key(self, symbol: str, start_date: str, end_date: str) -> str:
        """Generate cache key for data.
        
        Args:
            symbol: Stock symbol
            start_date: Start date
            end_date: End date
            
        Returns:
            Cache key string
        """
        key_string = f"{symbol}_{start_date}_{end_date}_{settings.data.interval}"
        return hashlib.md5(key_string.encode()).hexdigest()
    
    def _get_cache_file(self, cache_key: str) -> Path:
        """Get cache file path for a cache key.
        
        Args:
            cache_key: Cache key
            
        Returns:
            Path to cache file
        """
        return self.cache_dir / f"{cache_key}.csv"
    
    def _is_cache_valid(
        self,
        cache_key: str,
        start_date: str,
        end_date: str
    ) -> bool:
        """Check if cached data is still valid.
        
        Args:
            cache_key: Cache key
            start_date: Requested start date
            end_date: Requested end date
            
        Returns:
            True if cache is valid, False otherwise
        """
        if cache_key not in self.metadata:
            return False
        
        cache_info = self.metadata[cache_key]
        cache_file = self._get_cache_file(cache_key)
        
        # Check if file exists
        if not cache_file.exists():
            return False
        
        # Check if cache covers the requested date range
        cached_start = cache_info.get('start_date')
        cached_end = cache_info.get('end_date')
        
        if not cached_start or not cached_end:
            return False
        
        if cached_start > start_date or cached_end < end_date:
            return False
        
        # Check cache age
        cache_time = datetime.fromisoformat(cache_info.get('timestamp', '1970-01-01'))
        max_age = timedelta(hours=settings.data.cache_expiry_hours)
        
        if datetime.now() - cache_time > max_age:
            return False
        
        # For current day data, check if market has closed
        if end_date == datetime.now().strftime('%Y-%m-%d'):
            # If it's a weekday and after market hours, cache is valid
            now = datetime.now()
            if now.weekday() < 5 and now.hour >= 16:  # After 4 PM on weekday
                return True
            # Otherwise, cache expires more quickly for intraday data
            if datetime.now() - cache_time > timedelta(minutes=15):
                return False
        
        return True
    
    @log_performance
    def get_cached_data(
        self,
        symbol: str,
        start_date: str,
        end_date: str
    ) -> Optional[pd.DataFrame]:
        """Retrieve cached data if available and valid.
        
        Args:
            symbol: Stock symbol
            start_date: Start date in 'YYYY-MM-DD' format
            end_date: End date in 'YYYY-MM-DD' format
            
        Returns:
            Cached DataFrame or None if not available/valid
        """
        cache_key = self._get_cache_key(symbol, start_date, end_date)
        
        if not self._is_cache_valid(cache_key, start_date, end_date):
            return None
        
        try:
            cache_file = self._get_cache_file(cache_key)
            data = pd.read_csv(cache_file)
            
            # Explicitly convert 'date' column to datetime objects, parsing as UTC
            data['date'] = pd.to_datetime(data['date'], utc=True)
            
            # Ensure 'date' column is timezone-naive for consistent comparison
            if pd.api.types.is_datetime64_any_dtype(data['date']):
                if data['date'].dt.tz is not None:
                    data['date'] = data['date'].dt.tz_convert(None)
                
                # Filter to exact date range requested
                # Convert start_date and end_date strings to datetime objects for comparison
                start_datetime = datetime.strptime(start_date, '%Y-%m-%d')
                end_datetime = datetime.strptime(end_date, '%Y-%m-%d')
                
                data = data[
                    (data['date'] >= start_datetime) &
                    (data['date'] <= end_datetime)
                ].copy()
            else:
                 # This case should ideally not be reached after explicit conversion,
                 # but keep the warning/return None for safety.
                self.logger.warning(f"date column in cached data for {symbol} is not datetime-like after conversion.")
                return None # Treat cache as invalid if date column is not datetime
            
            self.logger.debug(
                f"Retrieved cached data for {symbol}: {len(data)} records"
            )
            return data
            
        except Exception as e:
            self.logger.warning(f"Failed to load cached data for {symbol}: {e}")
            return None
    
    @log_performance
    def cache_data(
        self,
        data: pd.DataFrame,
        symbol: str,
        start_date: str,
        end_date: str
    ) -> None:
        """Cache data to disk.
        
        Args:
            data: DataFrame to cache
            symbol: Stock symbol
            start_date: Start date
            end_date: End date
        """
        if data.empty:
            self.logger.warning(f"Not caching empty data for {symbol}")
            return
        
        cache_key = self._get_cache_key(symbol, start_date, end_date)
        cache_file = self._get_cache_file(cache_key)
        
        try:
            # Save data to CSV
            data.to_csv(cache_file, index=False)
            
            # Update metadata
            self.metadata[cache_key] = {
                'symbol': symbol,
                'start_date': start_date,
                'end_date': end_date,
                'timestamp': datetime.now().isoformat(),
                'records': len(data),
                'file_size': cache_file.stat().st_size,
                'interval': settings.data.interval
            }
            
            self._save_metadata()
            
            self.logger.debug(
                f"Cached {len(data)} records for {symbol} to {cache_file.name}"
            )
            
        except Exception as e:
            self.logger.error(f"Failed to cache data for {symbol}: {e}")
    
    def get_data_info(self, symbol: str) -> Dict[str, Any]:
        """Get information about cached data for a symbol.
        
        Args:
            symbol: Stock symbol
            
        Returns:
            Dictionary with cache information
        """
        symbol_caches = [
            {**info, 'cache_key': key}
            for key, info in self.metadata.items()
            if info.get('symbol') == symbol
        ]
        
        if not symbol_caches:
            return {'symbol': symbol, 'cached': False}
        
        # Get the most recent cache
        latest_cache = max(
            symbol_caches,
            key=lambda x: datetime.fromisoformat(x['timestamp'])
        )
        
        cache_file = self._get_cache_file(latest_cache['cache_key'])
        
        return {
            'symbol': symbol,
            'cached': True,
            'start_date': latest_cache['start_date'],
            'end_date': latest_cache['end_date'],
            'records': latest_cache['records'],
            'timestamp': latest_cache['timestamp'],
            'file_size': latest_cache['file_size'],
            'file_exists': cache_file.exists(),
            'age_hours': (
                datetime.now() - datetime.fromisoformat(latest_cache['timestamp'])
            ).total_seconds() / 3600
        }
    
    def clear_cache(self, symbol: Optional[str] = None) -> None:
        """Clear cached data.
        
        Args:
            symbol: Specific symbol to clear. If None, clears all cache
        """
        if symbol is None:
            # Clear all cache
            for cache_file in self.cache_dir.glob("*.csv"):
                try:
                    cache_file.unlink()
                except Exception as e:
                    self.logger.warning(f"Failed to delete {cache_file}: {e}")
            
            self.metadata.clear()
            self._save_metadata()
            self.logger.info("Cleared all cached data")
        else:
            # Clear cache for specific symbol
            keys_to_remove = [
                key for key, info in self.metadata.items()
                if info.get('symbol') == symbol
            ]
            
            for key in keys_to_remove:
                cache_file = self._get_cache_file(key)
                try:
                    if cache_file.exists():
                        cache_file.unlink()
                    del self.metadata[key]
                except Exception as e:
                    self.logger.warning(f"Failed to clear cache for {key}: {e}")
            
            self._save_metadata()
            self.logger.info(f"Cleared cached data for {symbol}")
    
    def cleanup_old_cache(self, max_age_days: int = 30) -> None:
        """Clean up old cache files.
        
        Args:
            max_age_days: Maximum age of cache files in days
        """
        cutoff_time = datetime.now() - timedelta(days=max_age_days)
        removed_count = 0
        
        keys_to_remove = []
        for key, info in self.metadata.items():
            cache_time = datetime.fromisoformat(info.get('timestamp', '1970-01-01'))
            if cache_time < cutoff_time:
                keys_to_remove.append(key)
        
        for key in keys_to_remove:
            cache_file = self._get_cache_file(key)
            try:
                if cache_file.exists():
                    cache_file.unlink()
                del self.metadata[key]
                removed_count += 1
            except Exception as e:
                self.logger.warning(f"Failed to remove old cache {key}: {e}")
        
        if removed_count > 0:
            self._save_metadata()
            self.logger.info(f"Cleaned up {removed_count} old cache files")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics.
        
        Returns:
            Dictionary with cache statistics
        """
        total_files = len(self.metadata)
        total_size = sum(info.get('file_size', 0) for info in self.metadata.values())
        
        symbols = set(info.get('symbol') for info in self.metadata.values())
        
        # Calculate age distribution
        now = datetime.now()
        ages = [
            (now - datetime.fromisoformat(info.get('timestamp', '1970-01-01'))).total_seconds() / 3600
            for info in self.metadata.values()
        ]
        
        return {
            'total_files': total_files,
            'total_size_mb': total_size / (1024 * 1024),
            'unique_symbols': len(symbols),
            'symbols': sorted(list(symbols)),
            'avg_age_hours': np.mean(ages) if ages else 0,
            'oldest_cache_hours': max(ages) if ages else 0,
            'newest_cache_hours': min(ages) if ages else 0
        }