2025-06-01 23:29:32.946 | INFO     | utils.logging:setup_logging:153 | [PID:26548] Logging initialized - Level: INFO, File: logs\trading_agent_main.log, Worker ID: main
2025-06-01 23:29:32.946 | INFO     | utils.logging:setup_logging:157 | [PID:26548] Worker logging setup complete - Worker ID: main
2025-06-01 23:29:32.946 | INFO     | __main__:cli:75 | FinRL Trading Agent CLI initialized
2025-06-01 23:29:44.924 | INFO     | __main__:train:650 | Starting SAC agent training
2025-06-01 23:29:44.925 | INFO     | __main__:train:654 | NumPy error reporting set to 'raise' for all floating point issues.
2025-06-01 23:29:45.160 | INFO     | data.fetcher:__init__:51 | DataFetcher initialized with cache dir: data\cache
2025-06-01 23:29:45.160 | INFO     | data.processor:__init__:36 | DataProcessor initialized with pandas_ta integration
2025-06-01 23:29:45.163 | INFO     | data.processor:process_stock_data:59 | Processing stock data: 22880 records
2025-06-01 23:29:45.188 | INFO     | data.processor:_add_technical_indicators:226 | [PID:26548] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 34): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric']
2025-06-01 23:29:45.843 | INFO     | data.fetcher:fetch_vix_data:250 | Fetching VIX data from 2016-03-15 to 2025-04-17
2025-06-01 23:29:45.889 | INFO     | utils.logging:wrapper:328 | Performance - DataCache.get_cached_data: 0.047s
2025-06-01 23:29:45.890 | INFO     | data.fetcher:fetch_vix_data:257 | Using cached VIX data
2025-06-01 23:29:45.890 | INFO     | utils.logging:wrapper:328 | Performance - DataFetcher.fetch_vix_data: 0.049s
2025-06-01 23:29:45.890 | INFO     | data.processor:process_vix_data:167 | [PID:26548] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 34): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric']
2025-06-01 23:29:45.891 | INFO     | data.processor:process_vix_data:168 | Processing VIX data: 2287 records
2025-06-01 23:29:45.914 | SUCCESS  | data.processor:process_vix_data:179 | Successfully processed VIX data: 2287 records
2025-06-01 23:29:45.914 | INFO     | utils.logging:wrapper:328 | Performance - DataProcessor.process_vix_data: 0.024s
2025-06-01 23:29:45.914 | INFO     | data.processor:process_stock_data:113 | Removing pre-existing VIX-related columns from stock data before merge: ['VIX_Change_5d', 'VIX_Regime', 'VIX_Regime_Numeric']
2025-06-01 23:29:45.937 | INFO     | data.processor:merge_with_vix:713 | [PID:26548] DataProcessor.merge_with_vix: Extended tech_indicator_list with VIX features (len: 41): ['VIX_MA_5', 'VIX_MA_20', 'VIX_Percentile_252', 'VIX_Change', 'VIX_Change_5d', 'VIX_Regime', 'VIX_Regime_Numeric']
2025-06-01 23:29:45.948 | INFO     | data.processor:merge_with_vix:733 | Merged stock data with VIX: 22880 records
2025-06-01 23:29:45.951 | INFO     | data.processor:process_stock_data:119 | Successfully merged VIX data during stock data processing.
2025-06-01 23:29:46.014 | INFO     | data.processor:_clean_processed_data:591 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-01 23:29:46.036 | INFO     | data.processor:_clean_processed_data:654 | Cleaned processed data: removed an additional 445 rows during final NaN check in feature/critical columns.
2025-06-01 23:29:46.050 | SUCCESS  | data.processor:process_stock_data:139 | Successfully processed data: 22435 records, 48 features
2025-06-01 23:29:46.050 | INFO     | utils.logging:wrapper:328 | Performance - DataProcessor.process_stock_data: 0.887s
2025-06-01 23:29:46.051 | INFO     | __main__:train:704 | --- Quick Stats for processed_for_env_df (after DataProcessor.process_stock_data) ---
2025-06-01 23:29:46.052 | INFO     | __main__:train:705 | Shape: (22435, 48)
2025-06-01 23:29:46.052 | INFO     | __main__:train:707 | Columns: ['Date', 'Open', 'High', 'Low', 'Close', 'Volume', 'Dividends', 'Stock Splits', 'tic', 'SMA_5', 'SMA_10', 'SMA_20', 'SMA_50', 'EMA_12', 'EMA_26', 'MACD_12_26_9', 'MACDh_12_26_9', 'MACDs_12_26_9', 'RSI_14', 'CCI_20', 'ADX_14', 'DMP_14', 'DMN_14', 'BBL_20_2.0', 'BBM_20_2.0', 'BBU_20_2.0', 'BBB_20_2.0', 'BBP_20_2.0', 'OBV', 'Price_Range', 'Price_Position', 'Returns_1d', 'Returns_5d', 'Returns_20d', 'Volume_MA_20', 'Volume_Ratio', 'Volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_pct_rank', 'vix_close_pct_change', 'VIX_MA_5', 'VIX_MA_20', 'VIX_Percentile_252', 'VIX_Change', 'VIX_Change_5d', 'VIX_Regime', 'VIX_Regime_Numeric']
2025-06-01 23:29:46.067 | INFO     | __main__:train:710 | NaN counts per column (if any):
Series([], dtype: int64)
2025-06-01 23:29:46.068 | INFO     | __main__:train:711 | Inf counts per column (if any):
Series([], dtype: int64)
2025-06-01 23:29:46.069 | INFO     | __main__:train:713 | Date range in processed_for_env_df ('Date'): 2016-03-15 00:00:00 to 2025-04-17 00:00:00
2025-06-01 23:29:46.071 | INFO     | __main__:train:715 | Unique 'tic' in processed_for_env_df: 10
2025-06-01 23:29:46.071 | INFO     | __main__:train:718 | --- End Quick Stats for processed_for_env_df ---
2025-06-01 23:29:46.071 | INFO     | __main__:train:729 | Assuming VIX indicators are pre-merged and correctly named in the loaded data.
2025-06-01 23:29:46.092 | INFO     | __main__:train:744 | Renaming columns to lowercase for FinRL: {'Open': 'open', 'High': 'high', 'Low': 'low', 'Close': 'close', 'Volume': 'volume'}
2025-06-01 23:29:46.094 | INFO     | __main__:train:748 | Training data: 17945 records, Validation data: 4490 records
2025-06-01 23:29:46.094 | INFO     | __main__:train:796 | Transforming data format for FinRL environment compatibility
2025-06-01 23:29:46.141 | INFO     | __main__:train:800 | Train_df columns before deduplication (showing only duplicated ones): ['vix_ma_5', 'vix_ma_20']
2025-06-01 23:29:46.146 | INFO     | __main__:train:802 | Train_df columns after deduplication: 46
2025-06-01 23:29:46.147 | INFO     | __main__:train:804 | Val_df columns before deduplication (showing only duplicated ones): ['vix_ma_5', 'vix_ma_20']
2025-06-01 23:29:46.150 | INFO     | __main__:train:806 | Val_df columns after deduplication: 46
2025-06-01 23:29:46.150 | INFO     | __main__:train:808 | Transformed training data: 17945 records, Validation data: 4490 records
2025-06-01 23:29:46.163 | INFO     | __main__:train:827 | Skipping set_index('Date') as 'day' index is already set by prepare_finrl_data.
2025-06-01 23:29:46.164 | INFO     | __main__:train:830 | Applying NaN fill and type check for technical indicators in training data.
2025-06-01 23:29:46.191 | INFO     | __main__:train:860 | Applying NaN fill and type check for technical indicators in validation data.
2025-06-01 23:29:46.217 | INFO     | __main__:train:935 | About to create AsymmetricTradingEnv with config: stock_dim=10, action_space=10, df_shape=(17945, 46)
2025-06-02 10:38:14.803 | INFO     | utils.logging:setup_logging:153 | [PID:71589] Logging initialized - Level: INFO, File: logs/trading_agent_main.log, Worker ID: main
2025-06-02 10:38:14.804 | INFO     | utils.logging:setup_logging:157 | [PID:71589] Worker logging setup complete - Worker ID: main
2025-06-02 10:38:14.804 | INFO     | __main__:cli:75 | FinRL Trading Agent CLI initialized
2025-06-02 10:38:15.655 | INFO     | __main__:get_data:91 | Starting data fetching process
2025-06-02 10:38:15.655 | INFO     | data.fetcher:__init__:51 | DataFetcher initialized with cache dir: data/cache
2025-06-02 10:38:15.655 | INFO     | __main__:get_data:101 | Fetching data for AAPL
2025-06-02 10:38:15.656 | INFO     | utils.logging:wrapper:328 | Performance - DataCache.get_cached_data: 0.000s
2025-06-02 10:38:15.656 | INFO     | data.fetcher:fetch_symbol_data:98 | Fetching data for AAPL from 2016-01-01 to 2025-04-19
2025-06-02 10:38:16.538 | INFO     | utils.logging:log_data_quality:206 | Data Quality - AAPL: 2337 points, 91.33% missing, 
2025-06-02 10:38:16.562 | INFO     | utils.logging:wrapper:328 | Performance - DataCache.cache_data: 0.024s
2025-06-02 10:38:16.562 | SUCCESS  | data.fetcher:fetch_symbol_data:141 | Successfully fetched 2337 records for AAPL
2025-06-02 10:38:16.563 | INFO     | utils.logging:wrapper:328 | Performance - DataFetcher.fetch_symbol_data: 0.907s
2025-06-02 10:38:16.563 | SUCCESS  | __main__:get_data:108 | Successfully fetched 2337 records for AAPL
2025-06-02 10:38:16.563 | INFO     | __main__:get_data:101 | Fetching data for MSFT
2025-06-02 10:38:16.563 | INFO     | utils.logging:wrapper:328 | Performance - DataCache.get_cached_data: 0.000s
2025-06-02 10:38:16.564 | INFO     | data.fetcher:fetch_symbol_data:98 | Fetching data for MSFT from 2016-01-01 to 2025-04-19
2025-06-02 10:38:16.850 | INFO     | utils.logging:log_data_quality:206 | Data Quality - MSFT: 2337 points, 92.04% missing, 
2025-06-02 10:38:16.868 | INFO     | utils.logging:wrapper:328 | Performance - DataCache.cache_data: 0.018s
2025-06-02 10:38:16.869 | SUCCESS  | data.fetcher:fetch_symbol_data:141 | Successfully fetched 2337 records for MSFT
2025-06-02 10:38:16.869 | INFO     | utils.logging:wrapper:328 | Performance - DataFetcher.fetch_symbol_data: 0.306s
2025-06-02 10:38:16.869 | SUCCESS  | __main__:get_data:108 | Successfully fetched 2337 records for MSFT
2025-06-02 10:38:16.869 | INFO     | __main__:get_data:101 | Fetching data for GOOGL
2025-06-02 10:38:16.870 | INFO     | utils.logging:wrapper:328 | Performance - DataCache.get_cached_data: 0.000s
2025-06-02 10:38:16.870 | INFO     | data.fetcher:fetch_symbol_data:98 | Fetching data for GOOGL from 2016-01-01 to 2025-04-19
2025-06-02 10:38:17.064 | INFO     | utils.logging:log_data_quality:206 | Data Quality - GOOGL: 2337 points, 90.82% missing, 
2025-06-02 10:38:17.084 | INFO     | utils.logging:wrapper:328 | Performance - DataCache.cache_data: 0.020s
2025-06-02 10:38:17.085 | SUCCESS  | data.fetcher:fetch_symbol_data:141 | Successfully fetched 2337 records for GOOGL
2025-06-02 10:38:17.085 | INFO     | utils.logging:wrapper:328 | Performance - DataFetcher.fetch_symbol_data: 0.215s
2025-06-02 10:38:17.085 | SUCCESS  | __main__:get_data:108 | Successfully fetched 2337 records for GOOGL
2025-06-02 10:38:17.085 | INFO     | __main__:get_data:101 | Fetching data for AMZN
2025-06-02 10:38:17.086 | INFO     | utils.logging:wrapper:328 | Performance - DataCache.get_cached_data: 0.000s
2025-06-02 10:38:17.086 | INFO     | data.fetcher:fetch_symbol_data:98 | Fetching data for AMZN from 2016-01-01 to 2025-04-19
2025-06-02 10:38:17.270 | INFO     | utils.logging:log_data_quality:206 | Data Quality - AMZN: 2337 points, 91.40% missing, 
2025-06-02 10:38:17.291 | INFO     | utils.logging:wrapper:328 | Performance - DataCache.cache_data: 0.020s
2025-06-02 10:38:17.291 | SUCCESS  | data.fetcher:fetch_symbol_data:141 | Successfully fetched 2337 records for AMZN
2025-06-02 10:38:17.291 | INFO     | utils.logging:wrapper:328 | Performance - DataFetcher.fetch_symbol_data: 0.206s
2025-06-02 10:38:17.292 | SUCCESS  | __main__:get_data:108 | Successfully fetched 2337 records for AMZN
2025-06-02 10:38:17.292 | INFO     | __main__:get_data:101 | Fetching data for META
2025-06-02 10:38:17.292 | INFO     | utils.logging:wrapper:328 | Performance - DataCache.get_cached_data: 0.000s
2025-06-02 10:38:17.292 | INFO     | data.fetcher:fetch_symbol_data:98 | Fetching data for META from 2016-01-01 to 2025-04-19
2025-06-02 10:38:17.670 | INFO     | utils.logging:log_data_quality:206 | Data Quality - META: 2337 points, 91.91% missing, 
2025-06-02 10:38:17.688 | INFO     | utils.logging:wrapper:328 | Performance - DataCache.cache_data: 0.017s
2025-06-02 10:38:17.688 | SUCCESS  | data.fetcher:fetch_symbol_data:141 | Successfully fetched 2337 records for META
2025-06-02 10:38:17.688 | INFO     | utils.logging:wrapper:328 | Performance - DataFetcher.fetch_symbol_data: 0.396s
2025-06-02 10:38:17.689 | SUCCESS  | __main__:get_data:108 | Successfully fetched 2337 records for META
2025-06-02 10:38:17.689 | INFO     | __main__:get_data:101 | Fetching data for NVDA
2025-06-02 10:38:17.689 | INFO     | utils.logging:wrapper:328 | Performance - DataCache.get_cached_data: 0.000s
2025-06-02 10:38:17.689 | INFO     | data.fetcher:fetch_symbol_data:98 | Fetching data for NVDA from 2016-01-01 to 2025-04-19
2025-06-02 10:38:17.894 | INFO     | utils.logging:log_data_quality:206 | Data Quality - NVDA: 2337 points, 92.49% missing, 
2025-06-02 10:38:17.913 | INFO     | utils.logging:wrapper:328 | Performance - DataCache.cache_data: 0.019s
2025-06-02 10:38:17.913 | SUCCESS  | data.fetcher:fetch_symbol_data:141 | Successfully fetched 2337 records for NVDA
2025-06-02 10:38:17.914 | INFO     | utils.logging:wrapper:328 | Performance - DataFetcher.fetch_symbol_data: 0.225s
2025-06-02 10:38:17.914 | SUCCESS  | __main__:get_data:108 | Successfully fetched 2337 records for NVDA
2025-06-02 10:38:17.914 | INFO     | __main__:get_data:101 | Fetching data for TSLA
2025-06-02 10:38:17.915 | INFO     | utils.logging:wrapper:328 | Performance - DataCache.get_cached_data: 0.000s
2025-06-02 10:38:17.915 | INFO     | data.fetcher:fetch_symbol_data:98 | Fetching data for TSLA from 2016-01-01 to 2025-04-19
2025-06-02 10:38:18.102 | INFO     | utils.logging:log_data_quality:206 | Data Quality - TSLA: 2337 points, 91.78% missing, 
2025-06-02 10:38:18.120 | INFO     | utils.logging:wrapper:328 | Performance - DataCache.cache_data: 0.018s
2025-06-02 10:38:18.120 | SUCCESS  | data.fetcher:fetch_symbol_data:141 | Successfully fetched 2337 records for TSLA
2025-06-02 10:38:18.121 | INFO     | utils.logging:wrapper:328 | Performance - DataFetcher.fetch_symbol_data: 0.206s
2025-06-02 10:38:18.121 | SUCCESS  | __main__:get_data:108 | Successfully fetched 2337 records for TSLA
2025-06-02 10:38:18.121 | INFO     | __main__:get_data:101 | Fetching data for AVGO
2025-06-02 10:38:18.121 | INFO     | utils.logging:wrapper:328 | Performance - DataCache.get_cached_data: 0.000s
2025-06-02 10:38:18.121 | INFO     | data.fetcher:fetch_symbol_data:98 | Fetching data for AVGO from 2016-01-01 to 2025-04-19
2025-06-02 10:38:18.320 | INFO     | utils.logging:log_data_quality:206 | Data Quality - AVGO: 2337 points, 93.07% missing, 
2025-06-02 10:38:18.338 | INFO     | utils.logging:wrapper:328 | Performance - DataCache.cache_data: 0.018s
2025-06-02 10:38:18.339 | SUCCESS  | data.fetcher:fetch_symbol_data:141 | Successfully fetched 2337 records for AVGO
2025-06-02 10:38:18.339 | INFO     | utils.logging:wrapper:328 | Performance - DataFetcher.fetch_symbol_data: 0.218s
2025-06-02 10:38:18.339 | SUCCESS  | __main__:get_data:108 | Successfully fetched 2337 records for AVGO
2025-06-02 10:38:18.339 | INFO     | __main__:get_data:101 | Fetching data for ADBE
2025-06-02 10:38:18.340 | INFO     | utils.logging:wrapper:328 | Performance - DataCache.get_cached_data: 0.000s
2025-06-02 10:38:18.340 | INFO     | data.fetcher:fetch_symbol_data:98 | Fetching data for ADBE from 2016-01-01 to 2025-04-19
2025-06-02 10:38:18.534 | INFO     | utils.logging:log_data_quality:206 | Data Quality - ADBE: 2337 points, 90.37% missing, 
2025-06-02 10:38:18.552 | INFO     | utils.logging:wrapper:328 | Performance - DataCache.cache_data: 0.018s
2025-06-02 10:38:18.552 | SUCCESS  | data.fetcher:fetch_symbol_data:141 | Successfully fetched 2337 records for ADBE
2025-06-02 10:38:18.552 | INFO     | utils.logging:wrapper:328 | Performance - DataFetcher.fetch_symbol_data: 0.213s
2025-06-02 10:38:18.553 | SUCCESS  | __main__:get_data:108 | Successfully fetched 2337 records for ADBE
2025-06-02 10:38:18.553 | INFO     | __main__:get_data:101 | Fetching data for ASML
2025-06-02 10:38:18.553 | INFO     | utils.logging:wrapper:328 | Performance - DataCache.get_cached_data: 0.000s
2025-06-02 10:38:18.553 | INFO     | data.fetcher:fetch_symbol_data:98 | Fetching data for ASML from 2016-01-01 to 2025-04-19
2025-06-02 10:38:18.745 | INFO     | utils.logging:log_data_quality:206 | Data Quality - ASML: 2337 points, 92.42% missing, 
2025-06-02 10:38:18.763 | INFO     | utils.logging:wrapper:328 | Performance - DataCache.cache_data: 0.018s
2025-06-02 10:38:18.763 | SUCCESS  | data.fetcher:fetch_symbol_data:141 | Successfully fetched 2337 records for ASML
2025-06-02 10:38:18.763 | INFO     | utils.logging:wrapper:328 | Performance - DataFetcher.fetch_symbol_data: 0.210s
2025-06-02 10:38:18.764 | SUCCESS  | __main__:get_data:108 | Successfully fetched 2337 records for ASML
2025-06-02 10:38:18.764 | INFO     | __main__:get_data:111 | Fetching VIX data
2025-06-02 10:38:18.764 | INFO     | data.fetcher:fetch_vix_data:250 | Fetching VIX data from 2016-01-01 to 2025-04-19
2025-06-02 10:38:18.765 | INFO     | utils.logging:wrapper:328 | Performance - DataCache.get_cached_data: 0.000s
2025-06-02 10:38:18.913 | INFO     | utils.logging:wrapper:328 | Performance - DataCache.cache_data: 0.017s
2025-06-02 10:38:18.913 | SUCCESS  | data.fetcher:fetch_vix_data:279 | Successfully fetched 2337 VIX records
2025-06-02 10:38:18.914 | INFO     | utils.logging:wrapper:328 | Performance - DataFetcher.fetch_vix_data: 0.150s
2025-06-02 10:38:18.914 | SUCCESS  | __main__:get_data:117 | Successfully fetched 2337 VIX records
2025-06-02 10:38:18.914 | SUCCESS  | __main__:get_data:119 | Data fetching completed successfully
2025-06-02 10:38:35.751 | INFO     | utils.logging:setup_logging:153 | [PID:71720] Logging initialized - Level: INFO, File: logs/trading_agent_main.log, Worker ID: main
2025-06-02 10:38:35.751 | INFO     | utils.logging:setup_logging:157 | [PID:71720] Worker logging setup complete - Worker ID: main
2025-06-02 10:38:35.751 | INFO     | __main__:cli:75 | FinRL Trading Agent CLI initialized
2025-06-02 10:38:36.120 | INFO     | __main__:process_data:137 | Starting data processing
2025-06-02 10:38:36.120 | INFO     | data.fetcher:__init__:51 | DataFetcher initialized with cache dir: data/cache
2025-06-02 10:38:36.121 | INFO     | data.processor:__init__:36 | DataProcessor initialized with pandas_ta integration
2025-06-02 10:38:36.121 | INFO     | __main__:process_data:157 | Fetching and processing data for 10 symbols...
2025-06-02 10:38:36.121 | INFO     | __main__:process_data:159 | Processing data for AAPL
2025-06-02 10:38:36.149 | INFO     | utils.logging:wrapper:328 | Performance - DataCache.get_cached_data: 0.027s
2025-06-02 10:38:36.149 | INFO     | data.fetcher:fetch_symbol_data:95 | Using cached data for AAPL
2025-06-02 10:38:36.149 | INFO     | utils.logging:wrapper:328 | Performance - DataFetcher.fetch_symbol_data: 0.028s
2025-06-02 10:38:36.149 | INFO     | data.processor:process_stock_data:59 | Processing stock data: 2337 records
2025-06-02 10:38:36.150 | INFO     | data.processor:process_stock_data:68 | Early rename of 'symbol' to 'tic' in process_stock_data.
2025-06-02 10:38:36.157 | INFO     | data.processor:_add_technical_indicators:226 | [PID:71720] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 34): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric']
2025-06-02 10:38:36.185 | INFO     | data.fetcher:fetch_vix_data:250 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-02 10:38:36.186 | INFO     | utils.logging:wrapper:328 | Performance - DataCache.get_cached_data: 0.000s
2025-06-02 10:38:36.951 | INFO     | utils.logging:wrapper:328 | Performance - DataCache.cache_data: 0.018s
2025-06-02 10:38:36.951 | SUCCESS  | data.fetcher:fetch_vix_data:279 | Successfully fetched 2337 VIX records
2025-06-02 10:38:36.951 | INFO     | utils.logging:wrapper:328 | Performance - DataFetcher.fetch_vix_data: 0.766s
2025-06-02 10:38:36.951 | INFO     | data.processor:process_vix_data:167 | [PID:71720] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 34): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric']
2025-06-02 10:38:36.952 | INFO     | data.processor:process_vix_data:168 | Processing VIX data: 2337 records
2025-06-02 10:38:36.959 | SUCCESS  | data.processor:process_vix_data:179 | Successfully processed VIX data: 2337 records
2025-06-02 10:38:36.959 | INFO     | utils.logging:wrapper:328 | Performance - DataProcessor.process_vix_data: 0.007s
2025-06-02 10:38:36.962 | INFO     | data.processor:merge_with_vix:713 | [PID:71720] DataProcessor.merge_with_vix: Extended tech_indicator_list with VIX features (len: 41): ['VIX_MA_5', 'VIX_MA_20', 'VIX_Percentile_252', 'VIX_Change', 'VIX_Change_5d', 'VIX_Regime', 'VIX_Regime_Numeric']
2025-06-02 10:38:36.964 | INFO     | data.processor:merge_with_vix:733 | Merged stock data with VIX: 2337 records
2025-06-02 10:38:36.964 | INFO     | data.processor:process_stock_data:119 | Successfully merged VIX data during stock data processing.
2025-06-02 10:38:36.970 | INFO     | data.processor:_clean_processed_data:591 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-02 10:38:36.973 | INFO     | data.processor:_clean_processed_data:654 | Cleaned processed data: removed an additional 49 rows during final NaN check in feature/critical columns.
2025-06-02 10:38:36.974 | SUCCESS  | data.processor:process_stock_data:139 | Successfully processed data: 2288 records, 44 features
2025-06-02 10:38:36.974 | INFO     | utils.logging:wrapper:328 | Performance - DataProcessor.process_stock_data: 0.825s
2025-06-02 10:38:36.975 | SUCCESS  | __main__:process_data:180 | Successfully processed data for AAPL
2025-06-02 10:38:36.975 | INFO     | __main__:process_data:159 | Processing data for MSFT
2025-06-02 10:38:36.988 | INFO     | utils.logging:wrapper:328 | Performance - DataCache.get_cached_data: 0.013s
2025-06-02 10:38:36.988 | INFO     | data.fetcher:fetch_symbol_data:95 | Using cached data for MSFT
2025-06-02 10:38:36.989 | INFO     | utils.logging:wrapper:328 | Performance - DataFetcher.fetch_symbol_data: 0.014s
2025-06-02 10:38:36.989 | INFO     | data.processor:process_stock_data:59 | Processing stock data: 2337 records
2025-06-02 10:38:36.989 | INFO     | data.processor:process_stock_data:68 | Early rename of 'symbol' to 'tic' in process_stock_data.
2025-06-02 10:38:36.993 | INFO     | data.processor:_add_technical_indicators:226 | [PID:71720] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 41): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'VIX_MA_5', 'VIX_MA_20', 'VIX_Percentile_252', 'VIX_Change', 'VIX_Change_5d', 'VIX_Regime', 'VIX_Regime_Numeric']
2025-06-02 10:38:37.018 | INFO     | data.fetcher:fetch_vix_data:250 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-02 10:38:37.032 | INFO     | utils.logging:wrapper:328 | Performance - DataCache.get_cached_data: 0.013s
2025-06-02 10:38:37.032 | INFO     | data.fetcher:fetch_vix_data:257 | Using cached VIX data
2025-06-02 10:38:37.032 | INFO     | utils.logging:wrapper:328 | Performance - DataFetcher.fetch_vix_data: 0.014s
2025-06-02 10:38:37.032 | INFO     | data.processor:process_vix_data:167 | [PID:71720] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 41): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'VIX_MA_5', 'VIX_MA_20', 'VIX_Percentile_252', 'VIX_Change', 'VIX_Change_5d', 'VIX_Regime', 'VIX_Regime_Numeric']
2025-06-02 10:38:37.032 | INFO     | data.processor:process_vix_data:168 | Processing VIX data: 2336 records
2025-06-02 10:38:37.039 | SUCCESS  | data.processor:process_vix_data:179 | Successfully processed VIX data: 2336 records
2025-06-02 10:38:37.039 | INFO     | utils.logging:wrapper:328 | Performance - DataProcessor.process_vix_data: 0.007s
2025-06-02 10:38:37.043 | INFO     | data.processor:merge_with_vix:733 | Merged stock data with VIX: 2337 records
2025-06-02 10:38:37.043 | INFO     | data.processor:process_stock_data:119 | Successfully merged VIX data during stock data processing.
2025-06-02 10:38:37.048 | INFO     | data.processor:_clean_processed_data:591 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-02 10:38:37.051 | INFO     | data.processor:_clean_processed_data:654 | Cleaned processed data: removed an additional 49 rows during final NaN check in feature/critical columns.
2025-06-02 10:38:37.052 | SUCCESS  | data.processor:process_stock_data:139 | Successfully processed data: 2288 records, 44 features
2025-06-02 10:38:37.052 | INFO     | utils.logging:wrapper:328 | Performance - DataProcessor.process_stock_data: 0.063s
2025-06-02 10:38:37.052 | SUCCESS  | __main__:process_data:180 | Successfully processed data for MSFT
2025-06-02 10:38:37.053 | INFO     | __main__:process_data:159 | Processing data for GOOGL
2025-06-02 10:38:37.067 | INFO     | utils.logging:wrapper:328 | Performance - DataCache.get_cached_data: 0.013s
2025-06-02 10:38:37.067 | INFO     | data.fetcher:fetch_symbol_data:95 | Using cached data for GOOGL
2025-06-02 10:38:37.067 | INFO     | utils.logging:wrapper:328 | Performance - DataFetcher.fetch_symbol_data: 0.014s
2025-06-02 10:38:37.068 | INFO     | data.processor:process_stock_data:59 | Processing stock data: 2337 records
2025-06-02 10:38:37.068 | INFO     | data.processor:process_stock_data:68 | Early rename of 'symbol' to 'tic' in process_stock_data.
2025-06-02 10:38:37.072 | INFO     | data.processor:_add_technical_indicators:226 | [PID:71720] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 41): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'VIX_MA_5', 'VIX_MA_20', 'VIX_Percentile_252', 'VIX_Change', 'VIX_Change_5d', 'VIX_Regime', 'VIX_Regime_Numeric']
2025-06-02 10:38:37.098 | INFO     | data.fetcher:fetch_vix_data:250 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-02 10:38:37.110 | INFO     | utils.logging:wrapper:328 | Performance - DataCache.get_cached_data: 0.012s
2025-06-02 10:38:37.111 | INFO     | data.fetcher:fetch_vix_data:257 | Using cached VIX data
2025-06-02 10:38:37.111 | INFO     | utils.logging:wrapper:328 | Performance - DataFetcher.fetch_vix_data: 0.013s
2025-06-02 10:38:37.111 | INFO     | data.processor:process_vix_data:167 | [PID:71720] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 41): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'VIX_MA_5', 'VIX_MA_20', 'VIX_Percentile_252', 'VIX_Change', 'VIX_Change_5d', 'VIX_Regime', 'VIX_Regime_Numeric']
2025-06-02 10:38:37.111 | INFO     | data.processor:process_vix_data:168 | Processing VIX data: 2336 records
2025-06-02 10:38:37.118 | SUCCESS  | data.processor:process_vix_data:179 | Successfully processed VIX data: 2336 records
2025-06-02 10:38:37.118 | INFO     | utils.logging:wrapper:328 | Performance - DataProcessor.process_vix_data: 0.007s
2025-06-02 10:38:37.122 | INFO     | data.processor:merge_with_vix:733 | Merged stock data with VIX: 2337 records
2025-06-02 10:38:37.122 | INFO     | data.processor:process_stock_data:119 | Successfully merged VIX data during stock data processing.
2025-06-02 10:38:37.127 | INFO     | data.processor:_clean_processed_data:591 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-02 10:38:37.130 | INFO     | data.processor:_clean_processed_data:654 | Cleaned processed data: removed an additional 49 rows during final NaN check in feature/critical columns.
2025-06-02 10:38:37.131 | SUCCESS  | data.processor:process_stock_data:139 | Successfully processed data: 2288 records, 44 features
2025-06-02 10:38:37.131 | INFO     | utils.logging:wrapper:328 | Performance - DataProcessor.process_stock_data: 0.063s
2025-06-02 10:38:37.131 | SUCCESS  | __main__:process_data:180 | Successfully processed data for GOOGL
2025-06-02 10:38:37.131 | INFO     | __main__:process_data:159 | Processing data for AMZN
2025-06-02 10:38:37.143 | INFO     | utils.logging:wrapper:328 | Performance - DataCache.get_cached_data: 0.012s
2025-06-02 10:38:37.144 | INFO     | data.fetcher:fetch_symbol_data:95 | Using cached data for AMZN
2025-06-02 10:38:37.144 | INFO     | utils.logging:wrapper:328 | Performance - DataFetcher.fetch_symbol_data: 0.013s
2025-06-02 10:38:37.144 | INFO     | data.processor:process_stock_data:59 | Processing stock data: 2337 records
2025-06-02 10:38:37.145 | INFO     | data.processor:process_stock_data:68 | Early rename of 'symbol' to 'tic' in process_stock_data.
2025-06-02 10:38:37.148 | INFO     | data.processor:_add_technical_indicators:226 | [PID:71720] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 41): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'VIX_MA_5', 'VIX_MA_20', 'VIX_Percentile_252', 'VIX_Change', 'VIX_Change_5d', 'VIX_Regime', 'VIX_Regime_Numeric']
2025-06-02 10:38:37.173 | INFO     | data.fetcher:fetch_vix_data:250 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-02 10:38:37.185 | INFO     | utils.logging:wrapper:328 | Performance - DataCache.get_cached_data: 0.012s
2025-06-02 10:38:37.186 | INFO     | data.fetcher:fetch_vix_data:257 | Using cached VIX data
2025-06-02 10:38:37.186 | INFO     | utils.logging:wrapper:328 | Performance - DataFetcher.fetch_vix_data: 0.013s
2025-06-02 10:38:37.186 | INFO     | data.processor:process_vix_data:167 | [PID:71720] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 41): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'VIX_MA_5', 'VIX_MA_20', 'VIX_Percentile_252', 'VIX_Change', 'VIX_Change_5d', 'VIX_Regime', 'VIX_Regime_Numeric']
2025-06-02 10:38:37.186 | INFO     | data.processor:process_vix_data:168 | Processing VIX data: 2336 records
2025-06-02 10:38:37.192 | SUCCESS  | data.processor:process_vix_data:179 | Successfully processed VIX data: 2336 records
2025-06-02 10:38:37.192 | INFO     | utils.logging:wrapper:328 | Performance - DataProcessor.process_vix_data: 0.006s
2025-06-02 10:38:37.196 | INFO     | data.processor:merge_with_vix:733 | Merged stock data with VIX: 2337 records
2025-06-02 10:38:37.196 | INFO     | data.processor:process_stock_data:119 | Successfully merged VIX data during stock data processing.
2025-06-02 10:38:37.201 | INFO     | data.processor:_clean_processed_data:591 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-02 10:38:37.204 | INFO     | data.processor:_clean_processed_data:654 | Cleaned processed data: removed an additional 49 rows during final NaN check in feature/critical columns.
2025-06-02 10:38:37.205 | SUCCESS  | data.processor:process_stock_data:139 | Successfully processed data: 2288 records, 44 features
2025-06-02 10:38:37.206 | INFO     | utils.logging:wrapper:328 | Performance - DataProcessor.process_stock_data: 0.061s
2025-06-02 10:38:37.206 | SUCCESS  | __main__:process_data:180 | Successfully processed data for AMZN
2025-06-02 10:38:37.206 | INFO     | __main__:process_data:159 | Processing data for META
2025-06-02 10:38:37.219 | INFO     | utils.logging:wrapper:328 | Performance - DataCache.get_cached_data: 0.013s
2025-06-02 10:38:37.220 | INFO     | data.fetcher:fetch_symbol_data:95 | Using cached data for META
2025-06-02 10:38:37.220 | INFO     | utils.logging:wrapper:328 | Performance - DataFetcher.fetch_symbol_data: 0.014s
2025-06-02 10:38:37.221 | INFO     | data.processor:process_stock_data:59 | Processing stock data: 2337 records
2025-06-02 10:38:37.221 | INFO     | data.processor:process_stock_data:68 | Early rename of 'symbol' to 'tic' in process_stock_data.
2025-06-02 10:38:37.224 | INFO     | data.processor:_add_technical_indicators:226 | [PID:71720] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 41): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'VIX_MA_5', 'VIX_MA_20', 'VIX_Percentile_252', 'VIX_Change', 'VIX_Change_5d', 'VIX_Regime', 'VIX_Regime_Numeric']
2025-06-02 10:38:37.250 | INFO     | data.fetcher:fetch_vix_data:250 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-02 10:38:37.263 | INFO     | utils.logging:wrapper:328 | Performance - DataCache.get_cached_data: 0.013s
2025-06-02 10:38:37.264 | INFO     | data.fetcher:fetch_vix_data:257 | Using cached VIX data
2025-06-02 10:38:37.264 | INFO     | utils.logging:wrapper:328 | Performance - DataFetcher.fetch_vix_data: 0.013s
2025-06-02 10:38:37.264 | INFO     | data.processor:process_vix_data:167 | [PID:71720] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 41): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'VIX_MA_5', 'VIX_MA_20', 'VIX_Percentile_252', 'VIX_Change', 'VIX_Change_5d', 'VIX_Regime', 'VIX_Regime_Numeric']
2025-06-02 10:38:37.264 | INFO     | data.processor:process_vix_data:168 | Processing VIX data: 2336 records
2025-06-02 10:38:37.271 | SUCCESS  | data.processor:process_vix_data:179 | Successfully processed VIX data: 2336 records
2025-06-02 10:38:37.271 | INFO     | utils.logging:wrapper:328 | Performance - DataProcessor.process_vix_data: 0.007s
2025-06-02 10:38:37.275 | INFO     | data.processor:merge_with_vix:733 | Merged stock data with VIX: 2337 records
2025-06-02 10:38:37.275 | INFO     | data.processor:process_stock_data:119 | Successfully merged VIX data during stock data processing.
2025-06-02 10:38:37.280 | INFO     | data.processor:_clean_processed_data:591 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-02 10:38:37.283 | INFO     | data.processor:_clean_processed_data:654 | Cleaned processed data: removed an additional 49 rows during final NaN check in feature/critical columns.
2025-06-02 10:38:37.284 | SUCCESS  | data.processor:process_stock_data:139 | Successfully processed data: 2288 records, 44 features
2025-06-02 10:38:37.284 | INFO     | utils.logging:wrapper:328 | Performance - DataProcessor.process_stock_data: 0.064s
2025-06-02 10:38:37.285 | SUCCESS  | __main__:process_data:180 | Successfully processed data for META
2025-06-02 10:38:37.285 | INFO     | __main__:process_data:159 | Processing data for NVDA
2025-06-02 10:38:37.298 | INFO     | utils.logging:wrapper:328 | Performance - DataCache.get_cached_data: 0.013s
2025-06-02 10:38:37.298 | INFO     | data.fetcher:fetch_symbol_data:95 | Using cached data for NVDA
2025-06-02 10:38:37.299 | INFO     | utils.logging:wrapper:328 | Performance - DataFetcher.fetch_symbol_data: 0.013s
2025-06-02 10:38:37.299 | INFO     | data.processor:process_stock_data:59 | Processing stock data: 2337 records
2025-06-02 10:38:37.299 | INFO     | data.processor:process_stock_data:68 | Early rename of 'symbol' to 'tic' in process_stock_data.
2025-06-02 10:38:37.303 | INFO     | data.processor:_add_technical_indicators:226 | [PID:71720] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 41): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'VIX_MA_5', 'VIX_MA_20', 'VIX_Percentile_252', 'VIX_Change', 'VIX_Change_5d', 'VIX_Regime', 'VIX_Regime_Numeric']
2025-06-02 10:38:37.330 | INFO     | data.fetcher:fetch_vix_data:250 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-02 10:38:37.344 | INFO     | utils.logging:wrapper:328 | Performance - DataCache.get_cached_data: 0.014s
2025-06-02 10:38:37.344 | INFO     | data.fetcher:fetch_vix_data:257 | Using cached VIX data
2025-06-02 10:38:37.345 | INFO     | utils.logging:wrapper:328 | Performance - DataFetcher.fetch_vix_data: 0.015s
2025-06-02 10:38:37.345 | INFO     | data.processor:process_vix_data:167 | [PID:71720] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 41): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'VIX_MA_5', 'VIX_MA_20', 'VIX_Percentile_252', 'VIX_Change', 'VIX_Change_5d', 'VIX_Regime', 'VIX_Regime_Numeric']
2025-06-02 10:38:37.345 | INFO     | data.processor:process_vix_data:168 | Processing VIX data: 2336 records
2025-06-02 10:38:37.352 | SUCCESS  | data.processor:process_vix_data:179 | Successfully processed VIX data: 2336 records
2025-06-02 10:38:37.352 | INFO     | utils.logging:wrapper:328 | Performance - DataProcessor.process_vix_data: 0.007s
2025-06-02 10:38:37.357 | INFO     | data.processor:merge_with_vix:733 | Merged stock data with VIX: 2337 records
2025-06-02 10:38:37.357 | INFO     | data.processor:process_stock_data:119 | Successfully merged VIX data during stock data processing.
2025-06-02 10:38:37.361 | INFO     | data.processor:_clean_processed_data:591 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-02 10:38:37.364 | INFO     | data.processor:_clean_processed_data:654 | Cleaned processed data: removed an additional 49 rows during final NaN check in feature/critical columns.
2025-06-02 10:38:37.365 | SUCCESS  | data.processor:process_stock_data:139 | Successfully processed data: 2288 records, 44 features
2025-06-02 10:38:37.365 | INFO     | utils.logging:wrapper:328 | Performance - DataProcessor.process_stock_data: 0.066s
2025-06-02 10:38:37.365 | SUCCESS  | __main__:process_data:180 | Successfully processed data for NVDA
2025-06-02 10:38:37.365 | INFO     | __main__:process_data:159 | Processing data for TSLA
2025-06-02 10:38:37.378 | INFO     | utils.logging:wrapper:328 | Performance - DataCache.get_cached_data: 0.013s
2025-06-02 10:38:37.379 | INFO     | data.fetcher:fetch_symbol_data:95 | Using cached data for TSLA
2025-06-02 10:38:37.379 | INFO     | utils.logging:wrapper:328 | Performance - DataFetcher.fetch_symbol_data: 0.014s
2025-06-02 10:38:37.379 | INFO     | data.processor:process_stock_data:59 | Processing stock data: 2337 records
2025-06-02 10:38:37.380 | INFO     | data.processor:process_stock_data:68 | Early rename of 'symbol' to 'tic' in process_stock_data.
2025-06-02 10:38:37.383 | INFO     | data.processor:_add_technical_indicators:226 | [PID:71720] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 41): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'VIX_MA_5', 'VIX_MA_20', 'VIX_Percentile_252', 'VIX_Change', 'VIX_Change_5d', 'VIX_Regime', 'VIX_Regime_Numeric']
2025-06-02 10:38:37.410 | INFO     | data.fetcher:fetch_vix_data:250 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-02 10:38:37.424 | INFO     | utils.logging:wrapper:328 | Performance - DataCache.get_cached_data: 0.013s
2025-06-02 10:38:37.424 | INFO     | data.fetcher:fetch_vix_data:257 | Using cached VIX data
2025-06-02 10:38:37.425 | INFO     | utils.logging:wrapper:328 | Performance - DataFetcher.fetch_vix_data: 0.014s
2025-06-02 10:38:37.425 | INFO     | data.processor:process_vix_data:167 | [PID:71720] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 41): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'VIX_MA_5', 'VIX_MA_20', 'VIX_Percentile_252', 'VIX_Change', 'VIX_Change_5d', 'VIX_Regime', 'VIX_Regime_Numeric']
2025-06-02 10:38:37.425 | INFO     | data.processor:process_vix_data:168 | Processing VIX data: 2336 records
2025-06-02 10:38:37.432 | SUCCESS  | data.processor:process_vix_data:179 | Successfully processed VIX data: 2336 records
2025-06-02 10:38:37.432 | INFO     | utils.logging:wrapper:328 | Performance - DataProcessor.process_vix_data: 0.007s
2025-06-02 10:38:37.436 | INFO     | data.processor:merge_with_vix:733 | Merged stock data with VIX: 2337 records
2025-06-02 10:38:37.437 | INFO     | data.processor:process_stock_data:119 | Successfully merged VIX data during stock data processing.
2025-06-02 10:38:37.441 | INFO     | data.processor:_clean_processed_data:591 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-02 10:38:37.445 | INFO     | data.processor:_clean_processed_data:654 | Cleaned processed data: removed an additional 49 rows during final NaN check in feature/critical columns.
2025-06-02 10:38:37.445 | SUCCESS  | data.processor:process_stock_data:139 | Successfully processed data: 2288 records, 44 features
2025-06-02 10:38:37.445 | INFO     | utils.logging:wrapper:328 | Performance - DataProcessor.process_stock_data: 0.066s
2025-06-02 10:38:37.445 | SUCCESS  | __main__:process_data:180 | Successfully processed data for TSLA
2025-06-02 10:38:37.446 | INFO     | __main__:process_data:159 | Processing data for AVGO
2025-06-02 10:38:37.459 | INFO     | utils.logging:wrapper:328 | Performance - DataCache.get_cached_data: 0.013s
2025-06-02 10:38:37.459 | INFO     | data.fetcher:fetch_symbol_data:95 | Using cached data for AVGO
2025-06-02 10:38:37.460 | INFO     | utils.logging:wrapper:328 | Performance - DataFetcher.fetch_symbol_data: 0.014s
2025-06-02 10:38:37.460 | INFO     | data.processor:process_stock_data:59 | Processing stock data: 2337 records
2025-06-02 10:38:37.461 | INFO     | data.processor:process_stock_data:68 | Early rename of 'symbol' to 'tic' in process_stock_data.
2025-06-02 10:38:37.464 | INFO     | data.processor:_add_technical_indicators:226 | [PID:71720] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 41): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'VIX_MA_5', 'VIX_MA_20', 'VIX_Percentile_252', 'VIX_Change', 'VIX_Change_5d', 'VIX_Regime', 'VIX_Regime_Numeric']
2025-06-02 10:38:37.490 | INFO     | data.fetcher:fetch_vix_data:250 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-02 10:38:37.502 | INFO     | utils.logging:wrapper:328 | Performance - DataCache.get_cached_data: 0.012s
2025-06-02 10:38:37.502 | INFO     | data.fetcher:fetch_vix_data:257 | Using cached VIX data
2025-06-02 10:38:37.503 | INFO     | utils.logging:wrapper:328 | Performance - DataFetcher.fetch_vix_data: 0.013s
2025-06-02 10:38:37.503 | INFO     | data.processor:process_vix_data:167 | [PID:71720] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 41): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'VIX_MA_5', 'VIX_MA_20', 'VIX_Percentile_252', 'VIX_Change', 'VIX_Change_5d', 'VIX_Regime', 'VIX_Regime_Numeric']
2025-06-02 10:38:37.503 | INFO     | data.processor:process_vix_data:168 | Processing VIX data: 2336 records
2025-06-02 10:38:37.509 | SUCCESS  | data.processor:process_vix_data:179 | Successfully processed VIX data: 2336 records
2025-06-02 10:38:37.509 | INFO     | utils.logging:wrapper:328 | Performance - DataProcessor.process_vix_data: 0.007s
2025-06-02 10:38:37.514 | INFO     | data.processor:merge_with_vix:733 | Merged stock data with VIX: 2337 records
2025-06-02 10:38:37.514 | INFO     | data.processor:process_stock_data:119 | Successfully merged VIX data during stock data processing.
2025-06-02 10:38:37.519 | INFO     | data.processor:_clean_processed_data:591 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-02 10:38:37.523 | INFO     | data.processor:_clean_processed_data:654 | Cleaned processed data: removed an additional 49 rows during final NaN check in feature/critical columns.
2025-06-02 10:38:37.524 | SUCCESS  | data.processor:process_stock_data:139 | Successfully processed data: 2288 records, 44 features
2025-06-02 10:38:37.524 | INFO     | utils.logging:wrapper:328 | Performance - DataProcessor.process_stock_data: 0.063s
2025-06-02 10:38:37.524 | SUCCESS  | __main__:process_data:180 | Successfully processed data for AVGO
2025-06-02 10:38:37.524 | INFO     | __main__:process_data:159 | Processing data for ADBE
2025-06-02 10:38:37.537 | INFO     | utils.logging:wrapper:328 | Performance - DataCache.get_cached_data: 0.013s
2025-06-02 10:38:37.537 | INFO     | data.fetcher:fetch_symbol_data:95 | Using cached data for ADBE
2025-06-02 10:38:37.538 | INFO     | utils.logging:wrapper:328 | Performance - DataFetcher.fetch_symbol_data: 0.014s
2025-06-02 10:38:37.538 | INFO     | data.processor:process_stock_data:59 | Processing stock data: 2337 records
2025-06-02 10:38:37.538 | INFO     | data.processor:process_stock_data:68 | Early rename of 'symbol' to 'tic' in process_stock_data.
2025-06-02 10:38:37.542 | INFO     | data.processor:_add_technical_indicators:226 | [PID:71720] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 41): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'VIX_MA_5', 'VIX_MA_20', 'VIX_Percentile_252', 'VIX_Change', 'VIX_Change_5d', 'VIX_Regime', 'VIX_Regime_Numeric']
2025-06-02 10:38:37.572 | INFO     | data.fetcher:fetch_vix_data:250 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-02 10:38:37.597 | INFO     | utils.logging:wrapper:328 | Performance - DataCache.get_cached_data: 0.024s
2025-06-02 10:38:37.597 | INFO     | data.fetcher:fetch_vix_data:257 | Using cached VIX data
2025-06-02 10:38:37.598 | INFO     | utils.logging:wrapper:328 | Performance - DataFetcher.fetch_vix_data: 0.026s
2025-06-02 10:38:37.599 | INFO     | data.processor:process_vix_data:167 | [PID:71720] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 41): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'VIX_MA_5', 'VIX_MA_20', 'VIX_Percentile_252', 'VIX_Change', 'VIX_Change_5d', 'VIX_Regime', 'VIX_Regime_Numeric']
2025-06-02 10:38:37.599 | INFO     | data.processor:process_vix_data:168 | Processing VIX data: 2336 records
2025-06-02 10:38:37.614 | SUCCESS  | data.processor:process_vix_data:179 | Successfully processed VIX data: 2336 records
2025-06-02 10:38:37.615 | INFO     | utils.logging:wrapper:328 | Performance - DataProcessor.process_vix_data: 0.016s
2025-06-02 10:38:37.622 | INFO     | data.processor:merge_with_vix:733 | Merged stock data with VIX: 2337 records
2025-06-02 10:38:37.622 | INFO     | data.processor:process_stock_data:119 | Successfully merged VIX data during stock data processing.
2025-06-02 10:38:37.630 | INFO     | data.processor:_clean_processed_data:591 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-02 10:38:37.635 | INFO     | data.processor:_clean_processed_data:654 | Cleaned processed data: removed an additional 49 rows during final NaN check in feature/critical columns.
2025-06-02 10:38:37.637 | SUCCESS  | data.processor:process_stock_data:139 | Successfully processed data: 2288 records, 44 features
2025-06-02 10:38:37.637 | INFO     | utils.logging:wrapper:328 | Performance - DataProcessor.process_stock_data: 0.099s
2025-06-02 10:38:37.637 | SUCCESS  | __main__:process_data:180 | Successfully processed data for ADBE
2025-06-02 10:38:37.637 | INFO     | __main__:process_data:159 | Processing data for ASML
2025-06-02 10:38:37.654 | INFO     | utils.logging:wrapper:328 | Performance - DataCache.get_cached_data: 0.017s
2025-06-02 10:38:37.655 | INFO     | data.fetcher:fetch_symbol_data:95 | Using cached data for ASML
2025-06-02 10:38:37.655 | INFO     | utils.logging:wrapper:328 | Performance - DataFetcher.fetch_symbol_data: 0.018s
2025-06-02 10:38:37.656 | INFO     | data.processor:process_stock_data:59 | Processing stock data: 2337 records
2025-06-02 10:38:37.656 | INFO     | data.processor:process_stock_data:68 | Early rename of 'symbol' to 'tic' in process_stock_data.
2025-06-02 10:38:37.661 | INFO     | data.processor:_add_technical_indicators:226 | [PID:71720] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 41): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'VIX_MA_5', 'VIX_MA_20', 'VIX_Percentile_252', 'VIX_Change', 'VIX_Change_5d', 'VIX_Regime', 'VIX_Regime_Numeric']
2025-06-02 10:38:37.692 | INFO     | data.fetcher:fetch_vix_data:250 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-02 10:38:37.705 | INFO     | utils.logging:wrapper:328 | Performance - DataCache.get_cached_data: 0.013s
2025-06-02 10:38:37.705 | INFO     | data.fetcher:fetch_vix_data:257 | Using cached VIX data
2025-06-02 10:38:37.706 | INFO     | utils.logging:wrapper:328 | Performance - DataFetcher.fetch_vix_data: 0.014s
2025-06-02 10:38:37.706 | INFO     | data.processor:process_vix_data:167 | [PID:71720] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 41): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'VIX_MA_5', 'VIX_MA_20', 'VIX_Percentile_252', 'VIX_Change', 'VIX_Change_5d', 'VIX_Regime', 'VIX_Regime_Numeric']
2025-06-02 10:38:37.706 | INFO     | data.processor:process_vix_data:168 | Processing VIX data: 2336 records
2025-06-02 10:38:37.713 | SUCCESS  | data.processor:process_vix_data:179 | Successfully processed VIX data: 2336 records
2025-06-02 10:38:37.713 | INFO     | utils.logging:wrapper:328 | Performance - DataProcessor.process_vix_data: 0.007s
2025-06-02 10:38:37.718 | INFO     | data.processor:merge_with_vix:733 | Merged stock data with VIX: 2337 records
2025-06-02 10:38:37.718 | INFO     | data.processor:process_stock_data:119 | Successfully merged VIX data during stock data processing.
2025-06-02 10:38:37.723 | INFO     | data.processor:_clean_processed_data:591 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-02 10:38:37.726 | INFO     | data.processor:_clean_processed_data:654 | Cleaned processed data: removed an additional 49 rows during final NaN check in feature/critical columns.
2025-06-02 10:38:37.727 | SUCCESS  | data.processor:process_stock_data:139 | Successfully processed data: 2288 records, 44 features
2025-06-02 10:38:37.727 | INFO     | utils.logging:wrapper:328 | Performance - DataProcessor.process_stock_data: 0.071s
2025-06-02 10:38:37.727 | SUCCESS  | __main__:process_data:180 | Successfully processed data for ASML
2025-06-02 10:38:37.752 | INFO     | __main__:process_data:200 | Combined data for all symbols: 22880 records
2025-06-02 10:38:37.753 | INFO     | __main__:process_data:203 | Fetching and processing VIX data
2025-06-02 10:38:37.753 | INFO     | data.fetcher:fetch_vix_data:250 | Fetching VIX data from 2016-01-01 to 2025-04-19
2025-06-02 10:38:37.765 | INFO     | utils.logging:wrapper:328 | Performance - DataCache.get_cached_data: 0.012s
2025-06-02 10:38:37.766 | INFO     | data.fetcher:fetch_vix_data:257 | Using cached VIX data
2025-06-02 10:38:37.766 | INFO     | utils.logging:wrapper:328 | Performance - DataFetcher.fetch_vix_data: 0.013s
2025-06-02 10:38:37.766 | INFO     | data.processor:process_vix_data:167 | [PID:71720] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 41): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'VIX_MA_5', 'VIX_MA_20', 'VIX_Percentile_252', 'VIX_Change', 'VIX_Change_5d', 'VIX_Regime', 'VIX_Regime_Numeric']
2025-06-02 10:38:37.766 | INFO     | data.processor:process_vix_data:168 | Processing VIX data: 2337 records
2025-06-02 10:38:37.773 | SUCCESS  | data.processor:process_vix_data:179 | Successfully processed VIX data: 2337 records
2025-06-02 10:38:37.774 | INFO     | utils.logging:wrapper:328 | Performance - DataProcessor.process_vix_data: 0.008s
2025-06-02 10:38:37.774 | SUCCESS  | __main__:process_data:215 | Successfully processed VIX data
2025-06-02 10:38:37.774 | INFO     | __main__:process_data:221 | Merging stock data with VIX data
2025-06-02 10:38:37.774 | INFO     | data.processor:merge_with_vix:689 | [PID:71720] Removing pre-existing VIX-related columns from stock_data in merge_with_vix: ['VIX_MA_5', 'VIX_MA_20', 'VIX_Percentile_252', 'VIX_Change', 'VIX_Change_5d', 'VIX_Regime', 'VIX_Regime_Numeric']
2025-06-02 10:38:37.786 | INFO     | data.processor:merge_with_vix:733 | Merged stock data with VIX: 22880 records
2025-06-02 10:38:37.787 | SUCCESS  | __main__:process_data:226 | Successfully merged data: 22880 records
2025-06-02 10:38:37.787 | INFO     | __main__:process_data:241 | Renamed VIX columns in final processed data: {'VIX_MA_5': 'vix_ma_5', 'VIX_MA_20': 'vix_ma_20', 'VIX_Percentile_252': 'vix_pct_rank', 'VIX_Change': 'vix_close_pct_change'}
2025-06-02 10:38:37.788 | INFO     | __main__:process_data:259 | Saving processed data to data/processed/processed_data.csv
2025-06-02 10:38:38.735 | SUCCESS  | __main__:process_data:261 | Processed data saved successfully
2025-06-02 10:38:38.735 | INFO     | __main__:process_data:264 | Final processed data summary:
2025-06-02 10:38:38.735 | INFO     | __main__:process_data:265 |   - Total records: 22880
2025-06-02 10:38:38.736 | INFO     | __main__:process_data:266 |   - Unique symbols: 10
2025-06-02 10:38:38.737 | INFO     | __main__:process_data:267 |   - Date range: 2016-03-15 00:00:00 to 2025-04-17 00:00:00
2025-06-02 10:38:38.737 | INFO     | __main__:process_data:268 |   - Columns: 44
2025-06-02 10:38:38.738 | INFO     | __main__:process_data:269 |   - Sample symbols: ['AAPL', 'ADBE', 'AMZN', 'ASML', 'AVGO']
2025-06-02 10:38:38.738 | SUCCESS  | __main__:process_data:271 | Data processing completed successfully
2025-06-02 10:40:38.499 | INFO     | utils.logging:setup_logging:153 | [PID:72235] Logging initialized - Level: INFO, File: logs/trading_agent_main.log, Worker ID: main
2025-06-02 10:40:38.499 | INFO     | utils.logging:setup_logging:157 | [PID:72235] Worker logging setup complete - Worker ID: main
2025-06-02 10:40:38.499 | INFO     | __main__:cli:75 | FinRL Trading Agent CLI initialized
2025-06-02 10:40:41.620 | INFO     | __main__:backtest:1096 | Starting backtesting
2025-06-02 10:40:41.620 | INFO     | backtesting.engine:run_backtest:50 | Starting backtest execution
2025-06-02 10:40:41.620 | INFO     | backtesting.engine:_load_backtest_data:108 | Loading processed data from data/processed/processed_data.csv
2025-06-02 10:40:41.755 | INFO     | backtesting.engine:run_backtest:55 | Loaded backtest data: 2510 records
2025-06-02 10:43:52.897 | INFO     | utils.logging:setup_logging:153 | [PID:73422] Logging initialized - Level: INFO, File: logs/trading_agent_main.log, Worker ID: main
2025-06-02 10:43:52.897 | INFO     | utils.logging:setup_logging:157 | [PID:73422] Worker logging setup complete - Worker ID: main
2025-06-02 10:43:52.897 | INFO     | __main__:cli:75 | FinRL Trading Agent CLI initialized
2025-06-02 10:43:55.539 | INFO     | __main__:backtest:1096 | Starting backtesting
2025-06-02 10:43:55.539 | INFO     | backtesting.engine:run_backtest:50 | Starting backtest execution
2025-06-02 10:43:55.540 | INFO     | backtesting.engine:_load_backtest_data:108 | Loading processed data from data/processed/processed_data.csv
2025-06-02 10:43:55.678 | INFO     | backtesting.engine:run_backtest:55 | Loaded backtest data: 2510 records
2025-06-02 10:44:50.490 | INFO     | utils.logging:setup_logging:153 | [PID:73670] Logging initialized - Level: INFO, File: logs/trading_agent_main.log, Worker ID: main
2025-06-02 10:44:50.490 | INFO     | utils.logging:setup_logging:157 | [PID:73670] Worker logging setup complete - Worker ID: main
2025-06-02 10:44:50.490 | INFO     | __main__:cli:75 | FinRL Trading Agent CLI initialized
2025-06-02 10:44:53.112 | INFO     | __main__:train:650 | Starting SAC agent training
2025-06-02 10:44:53.112 | INFO     | __main__:train:654 | NumPy error reporting set to 'raise' for all floating point issues.
2025-06-02 10:44:53.224 | INFO     | data.fetcher:__init__:51 | DataFetcher initialized with cache dir: data/cache
2025-06-02 10:44:53.224 | INFO     | data.processor:__init__:36 | DataProcessor initialized with pandas_ta integration
2025-06-02 10:44:53.226 | INFO     | data.processor:process_stock_data:59 | Processing stock data: 22880 records
2025-06-02 10:44:53.243 | INFO     | data.processor:_add_technical_indicators:226 | [PID:73670] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 34): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric']
2025-06-02 10:44:53.472 | INFO     | data.fetcher:fetch_vix_data:250 | Fetching VIX data from 2016-03-15 to 2025-04-17
2025-06-02 10:44:53.472 | INFO     | utils.logging:wrapper:328 | Performance - DataCache.get_cached_data: 0.000s
2025-06-02 10:44:54.344 | INFO     | utils.logging:wrapper:328 | Performance - DataCache.cache_data: 0.016s
2025-06-02 10:44:54.344 | SUCCESS  | data.fetcher:fetch_vix_data:279 | Successfully fetched 2287 VIX records
2025-06-02 10:44:54.345 | INFO     | utils.logging:wrapper:328 | Performance - DataFetcher.fetch_vix_data: 0.873s
2025-06-02 10:44:54.345 | INFO     | data.processor:process_vix_data:167 | [PID:73670] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 34): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric']
2025-06-02 10:44:54.345 | INFO     | data.processor:process_vix_data:168 | Processing VIX data: 2287 records
2025-06-02 10:44:54.352 | SUCCESS  | data.processor:process_vix_data:179 | Successfully processed VIX data: 2287 records
2025-06-02 10:44:54.352 | INFO     | utils.logging:wrapper:328 | Performance - DataProcessor.process_vix_data: 0.007s
2025-06-02 10:44:54.353 | INFO     | data.processor:process_stock_data:113 | Removing pre-existing VIX-related columns from stock data before merge: ['VIX_Change_5d', 'VIX_Regime', 'VIX_Regime_Numeric']
2025-06-02 10:44:54.367 | INFO     | data.processor:merge_with_vix:713 | [PID:73670] DataProcessor.merge_with_vix: Extended tech_indicator_list with VIX features (len: 41): ['VIX_MA_5', 'VIX_MA_20', 'VIX_Percentile_252', 'VIX_Change', 'VIX_Change_5d', 'VIX_Regime', 'VIX_Regime_Numeric']
2025-06-02 10:44:54.372 | INFO     | data.processor:merge_with_vix:733 | Merged stock data with VIX: 22880 records
2025-06-02 10:44:54.373 | INFO     | data.processor:process_stock_data:119 | Successfully merged VIX data during stock data processing.
2025-06-02 10:44:54.403 | INFO     | data.processor:_clean_processed_data:591 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-02 10:44:54.412 | INFO     | data.processor:_clean_processed_data:654 | Cleaned processed data: removed an additional 445 rows during final NaN check in feature/critical columns.
2025-06-02 10:44:54.417 | SUCCESS  | data.processor:process_stock_data:139 | Successfully processed data: 22435 records, 48 features
2025-06-02 10:44:54.417 | INFO     | utils.logging:wrapper:328 | Performance - DataProcessor.process_stock_data: 1.191s
2025-06-02 10:44:54.417 | INFO     | __main__:train:704 | --- Quick Stats for processed_for_env_df (after DataProcessor.process_stock_data) ---
2025-06-02 10:44:54.417 | INFO     | __main__:train:705 | Shape: (22435, 48)
2025-06-02 10:44:54.418 | INFO     | __main__:train:707 | Columns: ['Date', 'Open', 'High', 'Low', 'Close', 'Volume', 'Dividends', 'Stock Splits', 'tic', 'SMA_5', 'SMA_10', 'SMA_20', 'SMA_50', 'EMA_12', 'EMA_26', 'MACD_12_26_9', 'MACDh_12_26_9', 'MACDs_12_26_9', 'RSI_14', 'CCI_20', 'ADX_14', 'DMP_14', 'DMN_14', 'BBL_20_2.0', 'BBM_20_2.0', 'BBU_20_2.0', 'BBB_20_2.0', 'BBP_20_2.0', 'OBV', 'Price_Range', 'Price_Position', 'Returns_1d', 'Returns_5d', 'Returns_20d', 'Volume_MA_20', 'Volume_Ratio', 'Volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_pct_rank', 'vix_close_pct_change', 'VIX_MA_5', 'VIX_MA_20', 'VIX_Percentile_252', 'VIX_Change', 'VIX_Change_5d', 'VIX_Regime', 'VIX_Regime_Numeric']
2025-06-02 10:44:54.425 | INFO     | __main__:train:710 | NaN counts per column (if any):
Series([], dtype: int64)
2025-06-02 10:44:54.425 | INFO     | __main__:train:711 | Inf counts per column (if any):
Series([], dtype: int64)
2025-06-02 10:44:54.426 | INFO     | __main__:train:713 | Date range in processed_for_env_df ('Date'): 2016-03-15 00:00:00 to 2025-04-17 00:00:00
2025-06-02 10:44:54.427 | INFO     | __main__:train:715 | Unique 'tic' in processed_for_env_df: 10
2025-06-02 10:44:54.427 | INFO     | __main__:train:718 | --- End Quick Stats for processed_for_env_df ---
2025-06-02 10:44:54.427 | INFO     | __main__:train:729 | Assuming VIX indicators are pre-merged and correctly named in the loaded data.
2025-06-02 10:44:54.433 | INFO     | __main__:train:744 | Renaming columns to lowercase for FinRL: {'Open': 'open', 'High': 'high', 'Low': 'low', 'Close': 'close', 'Volume': 'volume'}
2025-06-02 10:44:54.434 | INFO     | __main__:train:748 | Training data: 17945 records, Validation data: 4490 records
2025-06-02 10:44:54.434 | INFO     | __main__:train:796 | Transforming data format for FinRL environment compatibility
2025-06-02 10:44:54.449 | INFO     | __main__:train:800 | Train_df columns before deduplication (showing only duplicated ones): ['vix_ma_5', 'vix_ma_20']
2025-06-02 10:44:54.451 | INFO     | __main__:train:802 | Train_df columns after deduplication: 46
2025-06-02 10:44:54.451 | INFO     | __main__:train:804 | Val_df columns before deduplication (showing only duplicated ones): ['vix_ma_5', 'vix_ma_20']
2025-06-02 10:44:54.452 | INFO     | __main__:train:806 | Val_df columns after deduplication: 46
2025-06-02 10:44:54.453 | INFO     | __main__:train:808 | Transformed training data: 17945 records, Validation data: 4490 records
2025-06-02 10:44:54.455 | INFO     | __main__:train:827 | Skipping set_index('Date') as 'day' index is already set by prepare_finrl_data.
2025-06-02 10:44:54.456 | INFO     | __main__:train:830 | Applying NaN fill and type check for technical indicators in training data.
2025-06-02 10:44:54.466 | INFO     | __main__:train:860 | Applying NaN fill and type check for technical indicators in validation data.
2025-06-02 10:44:54.477 | INFO     | __main__:train:935 | About to create AsymmetricTradingEnv with config: stock_dim=10, action_space=10, df_shape=(17945, 46)
2025-06-04 16:26:46.226 | INFO     | utils.logging:setup_logging:153 | [PID:167482] Logging initialized - Level: INFO, File: logs/trading_agent_main.log, Worker ID: main
2025-06-04 16:26:46.234 | INFO     | utils.logging:setup_logging:157 | [PID:167482] Worker logging setup complete - Worker ID: main
2025-06-04 16:26:46.236 | INFO     | __main__:cli:75 | FinRL Trading Agent CLI initialized
2025-06-04 16:26:53.616 | INFO     | __main__:get_data:91 | Starting data fetching process
2025-06-04 16:26:53.619 | INFO     | utils.logging:info:191 | DataFetcher initialized with cache dir: data/cache
2025-06-04 16:26:53.621 | INFO     | __main__:get_data:101 | Fetching data for AAPL
2025-06-04 16:26:53.625 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.001s
2025-06-04 16:26:53.630 | INFO     | utils.logging:info:191 | Fetching data for AAPL from 2016-01-01 to 2025-04-19
2025-06-04 16:26:57.312 | INFO     | utils.logging:log_data_quality:270 | Data Quality - AAPL: 2337 points, 91.33% missing, 
2025-06-04 16:26:57.634 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.cache_data: 0.318s
2025-06-04 16:26:57.636 | SUCCESS  | utils.logging:success:216 | Successfully fetched 2337 records for AAPL
2025-06-04 16:26:57.639 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_symbol_data: 4.017s
2025-06-04 16:26:57.642 | SUCCESS  | __main__:get_data:108 | Successfully fetched 2337 records for AAPL
2025-06-04 16:26:57.645 | INFO     | __main__:get_data:101 | Fetching data for MSFT
2025-06-04 16:26:57.647 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.000s
2025-06-04 16:26:57.649 | INFO     | utils.logging:info:191 | Fetching data for MSFT from 2016-01-01 to 2025-04-19
2025-06-04 16:26:58.275 | INFO     | utils.logging:log_data_quality:270 | Data Quality - MSFT: 2337 points, 92.04% missing, 
2025-06-04 16:26:58.519 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.cache_data: 0.242s
2025-06-04 16:26:58.522 | SUCCESS  | utils.logging:success:216 | Successfully fetched 2337 records for MSFT
2025-06-04 16:26:58.524 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_symbol_data: 0.878s
2025-06-04 16:26:58.528 | SUCCESS  | __main__:get_data:108 | Successfully fetched 2337 records for MSFT
2025-06-04 16:26:58.532 | INFO     | __main__:get_data:101 | Fetching data for GOOGL
2025-06-04 16:26:58.538 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.001s
2025-06-04 16:26:58.542 | INFO     | utils.logging:info:191 | Fetching data for GOOGL from 2016-01-01 to 2025-04-19
2025-06-04 16:26:59.410 | INFO     | utils.logging:log_data_quality:270 | Data Quality - GOOGL: 2337 points, 90.82% missing, 
2025-06-04 16:26:59.705 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.cache_data: 0.291s
2025-06-04 16:26:59.707 | SUCCESS  | utils.logging:success:216 | Successfully fetched 2337 records for GOOGL
2025-06-04 16:26:59.709 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_symbol_data: 1.172s
2025-06-04 16:26:59.714 | SUCCESS  | __main__:get_data:108 | Successfully fetched 2337 records for GOOGL
2025-06-04 16:26:59.717 | INFO     | __main__:get_data:101 | Fetching data for AMZN
2025-06-04 16:26:59.722 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.000s
2025-06-04 16:26:59.726 | INFO     | utils.logging:info:191 | Fetching data for AMZN from 2016-01-01 to 2025-04-19
2025-06-04 16:27:00.342 | INFO     | utils.logging:log_data_quality:270 | Data Quality - AMZN: 2337 points, 91.40% missing, 
2025-06-04 16:27:00.587 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.cache_data: 0.243s
2025-06-04 16:27:00.597 | SUCCESS  | utils.logging:success:216 | Successfully fetched 2337 records for AMZN
2025-06-04 16:27:00.602 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_symbol_data: 0.881s
2025-06-04 16:27:00.607 | SUCCESS  | __main__:get_data:108 | Successfully fetched 2337 records for AMZN
2025-06-04 16:27:00.611 | INFO     | __main__:get_data:101 | Fetching data for META
2025-06-04 16:27:00.614 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.000s
2025-06-04 16:27:00.616 | INFO     | utils.logging:info:191 | Fetching data for META from 2016-01-01 to 2025-04-19
2025-06-04 16:27:01.326 | INFO     | utils.logging:log_data_quality:270 | Data Quality - META: 2337 points, 91.91% missing, 
2025-06-04 16:27:01.556 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.cache_data: 0.228s
2025-06-04 16:27:01.557 | SUCCESS  | utils.logging:success:216 | Successfully fetched 2337 records for META
2025-06-04 16:27:01.562 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_symbol_data: 0.949s
2025-06-04 16:27:01.565 | SUCCESS  | __main__:get_data:108 | Successfully fetched 2337 records for META
2025-06-04 16:27:01.569 | INFO     | __main__:get_data:101 | Fetching data for NVDA
2025-06-04 16:27:01.572 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.000s
2025-06-04 16:27:01.575 | INFO     | utils.logging:info:191 | Fetching data for NVDA from 2016-01-01 to 2025-04-19
2025-06-04 16:27:02.078 | INFO     | utils.logging:log_data_quality:270 | Data Quality - NVDA: 2337 points, 92.49% missing, 
2025-06-04 16:27:02.363 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.cache_data: 0.282s
2025-06-04 16:27:02.365 | SUCCESS  | utils.logging:success:216 | Successfully fetched 2337 records for NVDA
2025-06-04 16:27:02.370 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_symbol_data: 0.798s
2025-06-04 16:27:02.374 | SUCCESS  | __main__:get_data:108 | Successfully fetched 2337 records for NVDA
2025-06-04 16:27:02.377 | INFO     | __main__:get_data:101 | Fetching data for TSLA
2025-06-04 16:27:02.382 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.000s
2025-06-04 16:27:02.384 | INFO     | utils.logging:info:191 | Fetching data for TSLA from 2016-01-01 to 2025-04-19
2025-06-04 16:27:02.946 | INFO     | utils.logging:log_data_quality:270 | Data Quality - TSLA: 2337 points, 91.78% missing, 
2025-06-04 16:27:03.226 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.cache_data: 0.276s
2025-06-04 16:27:03.229 | SUCCESS  | utils.logging:success:216 | Successfully fetched 2337 records for TSLA
2025-06-04 16:27:03.231 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_symbol_data: 0.849s
2025-06-04 16:27:03.236 | SUCCESS  | __main__:get_data:108 | Successfully fetched 2337 records for TSLA
2025-06-04 16:27:03.238 | INFO     | __main__:get_data:101 | Fetching data for AVGO
2025-06-04 16:27:03.242 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.000s
2025-06-04 16:27:03.246 | INFO     | utils.logging:info:191 | Fetching data for AVGO from 2016-01-01 to 2025-04-19
2025-06-04 16:27:03.932 | INFO     | utils.logging:log_data_quality:270 | Data Quality - AVGO: 2337 points, 93.07% missing, 
2025-06-04 16:27:04.151 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.cache_data: 0.217s
2025-06-04 16:27:04.153 | SUCCESS  | utils.logging:success:216 | Successfully fetched 2337 records for AVGO
2025-06-04 16:27:04.155 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_symbol_data: 0.913s
2025-06-04 16:27:04.157 | SUCCESS  | __main__:get_data:108 | Successfully fetched 2337 records for AVGO
2025-06-04 16:27:04.159 | INFO     | __main__:get_data:101 | Fetching data for ADBE
2025-06-04 16:27:04.162 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.000s
2025-06-04 16:27:04.163 | INFO     | utils.logging:info:191 | Fetching data for ADBE from 2016-01-01 to 2025-04-19
2025-06-04 16:27:04.724 | INFO     | utils.logging:log_data_quality:270 | Data Quality - ADBE: 2337 points, 90.37% missing, 
2025-06-04 16:27:04.938 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.cache_data: 0.212s
2025-06-04 16:27:04.940 | SUCCESS  | utils.logging:success:216 | Successfully fetched 2337 records for ADBE
2025-06-04 16:27:04.942 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_symbol_data: 0.780s
2025-06-04 16:27:04.945 | SUCCESS  | __main__:get_data:108 | Successfully fetched 2337 records for ADBE
2025-06-04 16:27:04.948 | INFO     | __main__:get_data:101 | Fetching data for ASML
2025-06-04 16:27:04.952 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.000s
2025-06-04 16:27:04.955 | INFO     | utils.logging:info:191 | Fetching data for ASML from 2016-01-01 to 2025-04-19
2025-06-04 16:27:05.687 | INFO     | utils.logging:log_data_quality:270 | Data Quality - ASML: 2337 points, 92.42% missing, 
2025-06-04 16:27:05.975 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.cache_data: 0.285s
2025-06-04 16:27:05.977 | SUCCESS  | utils.logging:success:216 | Successfully fetched 2337 records for ASML
2025-06-04 16:27:05.980 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_symbol_data: 1.028s
2025-06-04 16:27:05.983 | SUCCESS  | __main__:get_data:108 | Successfully fetched 2337 records for ASML
2025-06-04 16:27:05.986 | INFO     | __main__:get_data:111 | Fetching VIX data
2025-06-04 16:27:05.989 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-01 to 2025-04-19
2025-06-04 16:27:05.990 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.000s
2025-06-04 16:27:06.978 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.cache_data: 0.299s
2025-06-04 16:27:06.980 | SUCCESS  | utils.logging:success:216 | Successfully fetched 2337 VIX records
2025-06-04 16:27:06.984 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.996s
2025-06-04 16:27:06.987 | SUCCESS  | __main__:get_data:117 | Successfully fetched 2337 VIX records
2025-06-04 16:27:06.990 | SUCCESS  | __main__:get_data:119 | Data fetching completed successfully
2025-06-04 16:27:26.818 | INFO     | utils.logging:setup_logging:153 | [PID:167507] Logging initialized - Level: INFO, File: logs/trading_agent_main.log, Worker ID: main
2025-06-04 16:27:26.821 | INFO     | utils.logging:setup_logging:157 | [PID:167507] Worker logging setup complete - Worker ID: main
2025-06-04 16:27:26.823 | INFO     | __main__:cli:75 | FinRL Trading Agent CLI initialized
2025-06-04 16:27:31.809 | INFO     | __main__:process_data:137 | Starting data processing
2025-06-04 16:27:31.814 | INFO     | utils.logging:info:191 | DataFetcher initialized with cache dir: data/cache
2025-06-04 16:27:31.817 | INFO     | utils.logging:info:191 | DataProcessor initialized with pandas_ta integration
2025-06-04 16:27:31.819 | INFO     | __main__:process_data:157 | Fetching and processing data for 10 symbols...
2025-06-04 16:27:31.821 | INFO     | __main__:process_data:159 | Processing data for AAPL
2025-06-04 16:27:32.083 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.257s
2025-06-04 16:27:32.085 | INFO     | utils.logging:info:191 | Using cached data for AAPL
2025-06-04 16:27:32.087 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_symbol_data: 0.264s
2025-06-04 16:27:32.096 | INFO     | utils.logging:info:191 | Processing stock data: 2337 records
2025-06-04 16:27:32.102 | INFO     | utils.logging:info:191 | Early rename of 'symbol' to 'tic' in process_stock_data.
2025-06-04 16:27:32.190 | INFO     | utils.logging:info:191 | [PID:167507] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-04 16:27:32.731 | WARNING  | utils.logging:warning:196 | Need at least 2 tickers for turbulence calculation
2025-06-04 16:27:32.757 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-04 16:27:32.760 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.000s
2025-06-04 16:27:34.476 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.cache_data: 0.221s
2025-06-04 16:27:34.478 | SUCCESS  | utils.logging:success:216 | Successfully fetched 2337 VIX records
2025-06-04 16:27:34.484 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 1.742s
2025-06-04 16:27:34.488 | INFO     | utils.logging:info:191 | [PID:167507] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 35): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence']
2025-06-04 16:27:34.492 | INFO     | utils.logging:info:191 | Processing VIX data: 2337 records
2025-06-04 16:27:34.589 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2337 records
2025-06-04 16:27:34.591 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.103s
2025-06-04 16:27:34.635 | INFO     | utils.logging:info:191 | [PID:167507] DataProcessor.merge_with_vix: Extended tech_indicator_list with VIX features (len: 36): ['vix_regime']
2025-06-04 16:27:34.670 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 2337 records
2025-06-04 16:27:34.672 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-04 16:27:34.742 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-04 16:27:34.836 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 2337 records, 45 features
2025-06-04 16:27:34.838 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 2.743s
2025-06-04 16:27:34.841 | SUCCESS  | __main__:process_data:180 | Successfully processed data for AAPL
2025-06-04 16:27:34.842 | INFO     | __main__:process_data:159 | Processing data for MSFT
2025-06-04 16:27:34.991 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.145s
2025-06-04 16:27:34.993 | INFO     | utils.logging:info:191 | Using cached data for MSFT
2025-06-04 16:27:34.995 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_symbol_data: 0.150s
2025-06-04 16:27:34.999 | INFO     | utils.logging:info:191 | Processing stock data: 2337 records
2025-06-04 16:27:35.001 | INFO     | utils.logging:info:191 | Early rename of 'symbol' to 'tic' in process_stock_data.
2025-06-04 16:27:35.053 | INFO     | utils.logging:info:191 | [PID:167507] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 36): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence', 'vix_regime']
2025-06-04 16:27:35.362 | WARNING  | utils.logging:warning:196 | Need at least 2 tickers for turbulence calculation
2025-06-04 16:27:35.373 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-04 16:27:35.524 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.150s
2025-06-04 16:27:35.527 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-04 16:27:35.529 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.157s
2025-06-04 16:27:35.531 | INFO     | utils.logging:info:191 | [PID:167507] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 36): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence', 'vix_regime']
2025-06-04 16:27:35.534 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-04 16:27:35.616 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-04 16:27:35.618 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.086s
2025-06-04 16:27:35.685 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 2337 records
2025-06-04 16:27:35.687 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-04 16:27:35.755 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-04 16:27:35.895 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 2337 records, 45 features
2025-06-04 16:27:35.897 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 0.899s
2025-06-04 16:27:35.902 | SUCCESS  | __main__:process_data:180 | Successfully processed data for MSFT
2025-06-04 16:27:35.906 | INFO     | __main__:process_data:159 | Processing data for GOOGL
2025-06-04 16:27:36.103 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.194s
2025-06-04 16:27:36.105 | INFO     | utils.logging:info:191 | Using cached data for GOOGL
2025-06-04 16:27:36.107 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_symbol_data: 0.198s
2025-06-04 16:27:36.110 | INFO     | utils.logging:info:191 | Processing stock data: 2337 records
2025-06-04 16:27:36.118 | INFO     | utils.logging:info:191 | Early rename of 'symbol' to 'tic' in process_stock_data.
2025-06-04 16:27:36.177 | INFO     | utils.logging:info:191 | [PID:167507] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 36): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence', 'vix_regime']
2025-06-04 16:27:36.597 | WARNING  | utils.logging:warning:196 | Need at least 2 tickers for turbulence calculation
2025-06-04 16:27:36.609 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-04 16:27:36.797 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.184s
2025-06-04 16:27:36.798 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-04 16:27:36.802 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.194s
2025-06-04 16:27:36.806 | INFO     | utils.logging:info:191 | [PID:167507] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 36): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence', 'vix_regime']
2025-06-04 16:27:36.809 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-04 16:27:36.909 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-04 16:27:36.911 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.105s
2025-06-04 16:27:36.963 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 2337 records
2025-06-04 16:27:36.966 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-04 16:27:37.028 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-04 16:27:37.125 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 2337 records, 45 features
2025-06-04 16:27:37.127 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 1.017s
2025-06-04 16:27:37.130 | SUCCESS  | __main__:process_data:180 | Successfully processed data for GOOGL
2025-06-04 16:27:37.132 | INFO     | __main__:process_data:159 | Processing data for AMZN
2025-06-04 16:27:37.266 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.132s
2025-06-04 16:27:37.267 | INFO     | utils.logging:info:191 | Using cached data for AMZN
2025-06-04 16:27:37.269 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_symbol_data: 0.136s
2025-06-04 16:27:37.271 | INFO     | utils.logging:info:191 | Processing stock data: 2337 records
2025-06-04 16:27:37.278 | INFO     | utils.logging:info:191 | Early rename of 'symbol' to 'tic' in process_stock_data.
2025-06-04 16:27:37.324 | INFO     | utils.logging:info:191 | [PID:167507] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 36): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence', 'vix_regime']
2025-06-04 16:27:37.695 | WARNING  | utils.logging:warning:196 | Need at least 2 tickers for turbulence calculation
2025-06-04 16:27:37.706 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-04 16:27:37.928 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.220s
2025-06-04 16:27:37.930 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-04 16:27:37.932 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.227s
2025-06-04 16:27:37.936 | INFO     | utils.logging:info:191 | [PID:167507] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 36): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence', 'vix_regime']
2025-06-04 16:27:37.939 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-04 16:27:38.031 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-04 16:27:38.032 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.097s
2025-06-04 16:27:38.095 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 2337 records
2025-06-04 16:27:38.097 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-04 16:27:38.158 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-04 16:27:38.268 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 2337 records, 45 features
2025-06-04 16:27:38.271 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 1.000s
2025-06-04 16:27:38.274 | SUCCESS  | __main__:process_data:180 | Successfully processed data for AMZN
2025-06-04 16:27:38.277 | INFO     | __main__:process_data:159 | Processing data for META
2025-06-04 16:27:38.430 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.151s
2025-06-04 16:27:38.432 | INFO     | utils.logging:info:191 | Using cached data for META
2025-06-04 16:27:38.434 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_symbol_data: 0.155s
2025-06-04 16:27:38.438 | INFO     | utils.logging:info:191 | Processing stock data: 2337 records
2025-06-04 16:27:38.444 | INFO     | utils.logging:info:191 | Early rename of 'symbol' to 'tic' in process_stock_data.
2025-06-04 16:27:38.487 | INFO     | utils.logging:info:191 | [PID:167507] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 36): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence', 'vix_regime']
2025-06-04 16:27:38.848 | WARNING  | utils.logging:warning:196 | Need at least 2 tickers for turbulence calculation
2025-06-04 16:27:38.856 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-04 16:27:39.015 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.157s
2025-06-04 16:27:39.017 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-04 16:27:39.018 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.162s
2025-06-04 16:27:39.019 | INFO     | utils.logging:info:191 | [PID:167507] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 36): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence', 'vix_regime']
2025-06-04 16:27:39.023 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-04 16:27:39.105 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-04 16:27:39.108 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.088s
2025-06-04 16:27:39.157 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 2337 records
2025-06-04 16:27:39.160 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-04 16:27:39.232 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-04 16:27:39.317 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 2337 records, 45 features
2025-06-04 16:27:39.320 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 0.881s
2025-06-04 16:27:39.321 | SUCCESS  | __main__:process_data:180 | Successfully processed data for META
2025-06-04 16:27:39.323 | INFO     | __main__:process_data:159 | Processing data for NVDA
2025-06-04 16:27:39.465 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.141s
2025-06-04 16:27:39.467 | INFO     | utils.logging:info:191 | Using cached data for NVDA
2025-06-04 16:27:39.468 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_symbol_data: 0.144s
2025-06-04 16:27:39.473 | INFO     | utils.logging:info:191 | Processing stock data: 2337 records
2025-06-04 16:27:39.479 | INFO     | utils.logging:info:191 | Early rename of 'symbol' to 'tic' in process_stock_data.
2025-06-04 16:27:39.518 | INFO     | utils.logging:info:191 | [PID:167507] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 36): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence', 'vix_regime']
2025-06-04 16:27:39.874 | WARNING  | utils.logging:warning:196 | Need at least 2 tickers for turbulence calculation
2025-06-04 16:27:39.885 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-04 16:27:40.040 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.152s
2025-06-04 16:27:40.042 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-04 16:27:40.043 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.159s
2025-06-04 16:27:40.045 | INFO     | utils.logging:info:191 | [PID:167507] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 36): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence', 'vix_regime']
2025-06-04 16:27:40.049 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-04 16:27:40.132 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-04 16:27:40.134 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.088s
2025-06-04 16:27:40.190 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 2337 records
2025-06-04 16:27:40.193 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-04 16:27:40.244 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-04 16:27:40.341 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 2337 records, 45 features
2025-06-04 16:27:40.344 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 0.871s
2025-06-04 16:27:40.346 | SUCCESS  | __main__:process_data:180 | Successfully processed data for NVDA
2025-06-04 16:27:40.348 | INFO     | __main__:process_data:159 | Processing data for TSLA
2025-06-04 16:27:40.490 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.140s
2025-06-04 16:27:40.492 | INFO     | utils.logging:info:191 | Using cached data for TSLA
2025-06-04 16:27:40.495 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_symbol_data: 0.145s
2025-06-04 16:27:40.498 | INFO     | utils.logging:info:191 | Processing stock data: 2337 records
2025-06-04 16:27:40.504 | INFO     | utils.logging:info:191 | Early rename of 'symbol' to 'tic' in process_stock_data.
2025-06-04 16:27:40.551 | INFO     | utils.logging:info:191 | [PID:167507] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 36): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence', 'vix_regime']
2025-06-04 16:27:40.953 | WARNING  | utils.logging:warning:196 | Need at least 2 tickers for turbulence calculation
2025-06-04 16:27:40.961 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-04 16:27:41.131 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.168s
2025-06-04 16:27:41.133 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-04 16:27:41.134 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.173s
2025-06-04 16:27:41.136 | INFO     | utils.logging:info:191 | [PID:167507] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 36): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence', 'vix_regime']
2025-06-04 16:27:41.138 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-04 16:27:41.367 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-04 16:27:41.370 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.234s
2025-06-04 16:27:41.427 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 2337 records
2025-06-04 16:27:41.430 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-04 16:27:41.489 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-04 16:27:41.588 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 2337 records, 45 features
2025-06-04 16:27:41.590 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 1.092s
2025-06-04 16:27:41.592 | SUCCESS  | __main__:process_data:180 | Successfully processed data for TSLA
2025-06-04 16:27:41.593 | INFO     | __main__:process_data:159 | Processing data for AVGO
2025-06-04 16:27:41.763 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.168s
2025-06-04 16:27:41.765 | INFO     | utils.logging:info:191 | Using cached data for AVGO
2025-06-04 16:27:41.769 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_symbol_data: 0.174s
2025-06-04 16:27:41.774 | INFO     | utils.logging:info:191 | Processing stock data: 2337 records
2025-06-04 16:27:41.780 | INFO     | utils.logging:info:191 | Early rename of 'symbol' to 'tic' in process_stock_data.
2025-06-04 16:27:41.828 | INFO     | utils.logging:info:191 | [PID:167507] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 36): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence', 'vix_regime']
2025-06-04 16:27:42.158 | WARNING  | utils.logging:warning:196 | Need at least 2 tickers for turbulence calculation
2025-06-04 16:27:42.164 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-04 16:27:42.303 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.135s
2025-06-04 16:27:42.305 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-04 16:27:42.307 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.143s
2025-06-04 16:27:42.309 | INFO     | utils.logging:info:191 | [PID:167507] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 36): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence', 'vix_regime']
2025-06-04 16:27:42.312 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-04 16:27:42.391 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-04 16:27:42.392 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.083s
2025-06-04 16:27:42.440 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 2337 records
2025-06-04 16:27:42.442 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-04 16:27:42.494 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-04 16:27:42.577 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 2337 records, 45 features
2025-06-04 16:27:42.579 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 0.805s
2025-06-04 16:27:42.582 | SUCCESS  | __main__:process_data:180 | Successfully processed data for AVGO
2025-06-04 16:27:42.583 | INFO     | __main__:process_data:159 | Processing data for ADBE
2025-06-04 16:27:42.714 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.127s
2025-06-04 16:27:42.715 | INFO     | utils.logging:info:191 | Using cached data for ADBE
2025-06-04 16:27:42.717 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_symbol_data: 0.130s
2025-06-04 16:27:42.721 | INFO     | utils.logging:info:191 | Processing stock data: 2337 records
2025-06-04 16:27:42.724 | INFO     | utils.logging:info:191 | Early rename of 'symbol' to 'tic' in process_stock_data.
2025-06-04 16:27:42.767 | INFO     | utils.logging:info:191 | [PID:167507] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 36): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence', 'vix_regime']
2025-06-04 16:27:43.088 | WARNING  | utils.logging:warning:196 | Need at least 2 tickers for turbulence calculation
2025-06-04 16:27:43.094 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-04 16:27:43.232 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.136s
2025-06-04 16:27:43.234 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-04 16:27:43.235 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.141s
2025-06-04 16:27:43.238 | INFO     | utils.logging:info:191 | [PID:167507] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 36): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence', 'vix_regime']
2025-06-04 16:27:43.242 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-04 16:27:43.340 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-04 16:27:43.342 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.104s
2025-06-04 16:27:43.409 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 2337 records
2025-06-04 16:27:43.413 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-04 16:27:43.487 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-04 16:27:43.638 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 2337 records, 45 features
2025-06-04 16:27:43.641 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 0.920s
2025-06-04 16:27:43.643 | SUCCESS  | __main__:process_data:180 | Successfully processed data for ADBE
2025-06-04 16:27:43.645 | INFO     | __main__:process_data:159 | Processing data for ASML
2025-06-04 16:27:43.865 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.217s
2025-06-04 16:27:43.867 | INFO     | utils.logging:info:191 | Using cached data for ASML
2025-06-04 16:27:43.873 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_symbol_data: 0.225s
2025-06-04 16:27:43.877 | INFO     | utils.logging:info:191 | Processing stock data: 2337 records
2025-06-04 16:27:43.882 | INFO     | utils.logging:info:191 | Early rename of 'symbol' to 'tic' in process_stock_data.
2025-06-04 16:27:43.951 | INFO     | utils.logging:info:191 | [PID:167507] DataProcessor.add_technical_indicators: Initial tech_indicator_list (len: 36): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence', 'vix_regime']
2025-06-04 16:27:44.493 | WARNING  | utils.logging:warning:196 | Need at least 2 tickers for turbulence calculation
2025-06-04 16:27:44.503 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-04 to 2025-04-17
2025-06-04 16:27:44.662 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.155s
2025-06-04 16:27:44.664 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-04 16:27:44.666 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.163s
2025-06-04 16:27:44.668 | INFO     | utils.logging:info:191 | [PID:167507] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 36): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence', 'vix_regime']
2025-06-04 16:27:44.674 | INFO     | utils.logging:info:191 | Processing VIX data: 2336 records
2025-06-04 16:27:44.750 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2336 records
2025-06-04 16:27:44.752 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.084s
2025-06-04 16:27:44.816 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 2337 records
2025-06-04 16:27:44.820 | INFO     | utils.logging:info:191 | Successfully merged VIX data during stock data processing.
2025-06-04 16:27:44.895 | INFO     | utils.logging:info:191 | PROCESSOR_CLEAN: Starting NaN fill for technical/VIX indicators.
2025-06-04 16:27:45.017 | SUCCESS  | utils.logging:success:216 | Successfully processed data: 2337 records, 45 features
2025-06-04 16:27:45.019 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_stock_data: 1.143s
2025-06-04 16:27:45.021 | SUCCESS  | __main__:process_data:180 | Successfully processed data for ASML
2025-06-04 16:27:45.271 | INFO     | __main__:process_data:200 | Combined data for all symbols: 23370 records
2025-06-04 16:27:45.273 | INFO     | __main__:process_data:203 | Fetching and processing VIX data
2025-06-04 16:27:45.275 | INFO     | utils.logging:info:191 | Fetching VIX data from 2016-01-01 to 2025-04-19
2025-06-04 16:27:45.429 | INFO     | utils.logging:wrapper:392 | Performance - DataCache.get_cached_data: 0.150s
2025-06-04 16:27:45.431 | INFO     | utils.logging:info:191 | Using cached VIX data
2025-06-04 16:27:45.433 | INFO     | utils.logging:wrapper:392 | Performance - DataFetcher.fetch_vix_data: 0.159s
2025-06-04 16:27:45.435 | INFO     | utils.logging:info:191 | [PID:167507] DataProcessor.add_vix: Current tech_indicator_list before VIX (len: 36): ['sma_5', 'sma_10', 'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi_14', 'cci_20', 'macd_12_26_9', 'macdh_12_26_9', 'macds_12_26_9', 'adx_14', 'dmp_14', 'dmn_14', 'bbl_20_2.0', 'bbm_20_2.0', 'bbu_20_2.0', 'bbb_20_2.0', 'bbp_20_2.0', 'obv', 'price_range', 'price_position', 'returns_1d', 'returns_5d', 'returns_20d', 'volume_ma_20', 'volume_ratio', 'volatility_20d', 'vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime_numeric', 'turbulence', 'vix_regime']
2025-06-04 16:27:45.440 | INFO     | utils.logging:info:191 | Processing VIX data: 2337 records
2025-06-04 16:27:45.533 | SUCCESS  | utils.logging:success:216 | Successfully processed VIX data: 2337 records
2025-06-04 16:27:45.535 | INFO     | utils.logging:wrapper:392 | Performance - DataProcessor.process_vix_data: 0.099s
2025-06-04 16:27:45.536 | SUCCESS  | __main__:process_data:215 | Successfully processed VIX data
2025-06-04 16:27:45.539 | INFO     | __main__:process_data:221 | Merging stock data with VIX data
2025-06-04 16:27:45.559 | INFO     | utils.logging:info:191 | [PID:167507] Removing pre-existing VIX-related columns from stock_data in merge_with_vix: ['vix_ma_5', 'vix_ma_20', 'vix_percentile_252', 'vix_change', 'vix_change_5d', 'vix_regime', 'vix_regime_numeric']
2025-06-04 16:27:45.690 | INFO     | utils.logging:info:191 | Merged stock data with VIX: 23370 records
2025-06-04 16:27:45.692 | SUCCESS  | __main__:process_data:226 | Successfully merged data: 23370 records
2025-06-04 16:27:45.696 | INFO     | __main__:process_data:257 | Saving processed data to data/processed/processed_data.csv
2025-06-04 16:27:55.312 | SUCCESS  | __main__:process_data:259 | Processed data saved successfully
2025-06-04 16:27:55.314 | INFO     | __main__:process_data:262 | Final processed data summary:
2025-06-04 16:27:55.317 | INFO     | __main__:process_data:263 |   - Total records: 23370
2025-06-04 16:27:55.323 | INFO     | __main__:process_data:264 |   - Unique symbols: 10
2025-06-04 16:27:55.333 | INFO     | __main__:process_data:265 |   - Date range: 2016-01-04 00:00:00 to 2025-04-17 00:00:00
2025-06-04 16:27:55.335 | INFO     | __main__:process_data:266 |   - Columns: 45
2025-06-04 16:27:55.342 | INFO     | __main__:process_data:267 |   - Sample symbols: ['AAPL', 'ADBE', 'AMZN', 'ASML', 'AVGO']
2025-06-04 16:27:55.343 | SUCCESS  | __main__:process_data:269 | Data processing completed successfully
